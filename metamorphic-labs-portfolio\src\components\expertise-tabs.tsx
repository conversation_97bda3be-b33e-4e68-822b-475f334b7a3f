'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Brain, Cog, Code, CheckCircle, ArrowRight } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

const expertiseAreas = [
  {
    id: 'ai-platforms',
    title: 'AI Platforms',
    icon: Brain,
    color: 'secondary',
    description: 'Adaptive AI systems that learn, evolve, and optimize performance across multiple models and use cases.',
    capabilities: [
      'Multi-Model AI Integration',
      'Adaptive Learning Systems',
      'Real-time Performance Optimization',
      'Intelligent Routing & Load Balancing',
      'Custom Model Training & Fine-tuning',
      'Enterprise-Grade Security & Compliance'
    ],
    technologies: ['OpenAI GPT-4', 'Anthropic Claude', 'Google Gemini', 'Hugging Face', 'TensorFlow', 'PyTorch'],
    projects: ['Metamorphic AI Platform', 'AI Integration & Broker'],
    benefits: [
      '40% reduction in AI implementation time',
      '60% improvement in model performance',
      '99.9% uptime with intelligent failover',
      '50% cost savings through optimization'
    ]
  },
  {
    id: 'intelligent-automation',
    title: 'Intelligent Automation',
    icon: Cog,
    color: 'tertiary',
    description: 'Self-optimizing systems that automate complex workflows while continuously improving efficiency and accuracy.',
    capabilities: [
      'Self-Optimizing CI/CD Pipelines',
      'Predictive Anomaly Detection',
      'Automated Quality Assurance',
      'Dynamic Resource Management',
      'Intelligent Process Orchestration',
      'Real-time Performance Analytics'
    ],
    technologies: ['Kubernetes', 'Docker', 'Jenkins', 'GitLab CI', 'Prometheus', 'Grafana'],
    projects: ['Living Pipeline', 'Metamorphic Testing'],
    benefits: [
      '70% reduction in deployment failures',
      '85% faster build and test cycles',
      '60% decrease in manual intervention',
      '90% improvement in resource utilization'
    ]
  },
  {
    id: 'custom-software',
    title: 'Custom Software',
    icon: Code,
    color: 'accent',
    description: 'Bespoke solutions built with cutting-edge technologies to solve unique business challenges and drive growth.',
    capabilities: [
      'Full-Stack Web Applications',
      'Mobile App Development',
      'Cloud-Native Architecture',
      'Microservices Design',
      'API Development & Integration',
      'Database Design & Optimization'
    ],
    technologies: ['React', 'Next.js', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS/GCP/Azure'],
    projects: ['Metamorphic SaaS Suite', 'Metamorphic Reactor'],
    benefits: [
      '3x faster development cycles',
      '99.9% application uptime',
      '50% reduction in maintenance costs',
      '100% client satisfaction rate'
    ]
  }
]

export function ExpertiseTabs() {
  const [activeTab, setActiveTab] = useState('ai-platforms')
  const activeArea = expertiseAreas.find(area => area.id === activeTab)!

  return (
    <section className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Tab Navigation */}
        <div className="flex flex-col sm:flex-row justify-center mb-12">
          <div className="flex flex-col sm:flex-row bg-surface-alt rounded-2xl p-2 gap-2">
            {expertiseAreas.map((area) => {
              const Icon = area.icon
              return (
                <button
                  key={area.id}
                  onClick={() => setActiveTab(area.id)}
                  className={`flex items-center gap-3 px-6 py-4 rounded-xl transition-all duration-300 ${
                    activeTab === area.id
                      ? `bg-${area.color} text-white shadow-lg`
                      : 'text-text-secondary hover:text-text-primary hover:bg-surface'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{area.title}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
              {/* Left Column - Overview */}
              <div className="space-y-8">
                <div>
                  <div className="flex items-center gap-3 mb-4">
                    <div className={`flex h-12 w-12 items-center justify-center rounded-full bg-${activeArea.color}/10`}>
                      <activeArea.icon className={`h-6 w-6 text-${activeArea.color}`} />
                    </div>
                    <h2 className="text-3xl font-bold text-text-primary">
                      {activeArea.title}
                    </h2>
                  </div>
                  <p className="text-lg text-text-secondary leading-relaxed">
                    {activeArea.description}
                  </p>
                </div>

                {/* Capabilities */}
                <Card className="bg-surface-alt border-secondary/20">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-text-primary mb-4">
                      Core Capabilities
                    </h3>
                    <div className="grid grid-cols-1 gap-3">
                      {activeArea.capabilities.map((capability, index) => (
                        <motion.div
                          key={capability}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="flex items-center gap-3"
                        >
                          <CheckCircle className={`h-5 w-5 text-${activeArea.color} flex-shrink-0`} />
                          <span className="text-text-secondary">{capability}</span>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Technologies */}
                <div>
                  <h3 className="text-xl font-semibold text-text-primary mb-4">
                    Technologies & Tools
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {activeArea.technologies.map((tech) => (
                      <Badge
                        key={tech}
                        variant="secondary"
                        className={`bg-${activeArea.color}/10 text-${activeArea.color} border-${activeArea.color}/20`}
                      >
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Right Column - Benefits & Projects */}
              <div className="space-y-8">
                {/* Key Benefits */}
                <Card className="bg-surface-alt border-secondary/20">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-text-primary mb-4">
                      Proven Results
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {activeArea.benefits.map((benefit, index) => (
                        <motion.div
                          key={benefit}
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className={`p-4 rounded-lg bg-${activeArea.color}/5 border border-${activeArea.color}/20`}
                        >
                          <span className="text-text-primary font-medium">{benefit}</span>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Related Projects */}
                <Card className="bg-surface-alt border-secondary/20">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-text-primary mb-4">
                      Featured Projects
                    </h3>
                    <div className="space-y-3">
                      {activeArea.projects.map((project) => (
                        <div
                          key={project}
                          className="flex items-center justify-between p-3 rounded-lg bg-surface hover:bg-surface/80 transition-colors"
                        >
                          <span className="text-text-primary font-medium">{project}</span>
                          <ArrowRight className="h-4 w-4 text-text-secondary" />
                        </div>
                      ))}
                    </div>
                    <div className="mt-6">
                      <Button asChild variant="outline" className="w-full">
                        <Link href="/projects">
                          View All Projects
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* CTA */}
                <Card className={`bg-gradient-to-br from-${activeArea.color}/10 to-${activeArea.color}/5 border-${activeArea.color}/20`}>
                  <CardContent className="p-6 text-center">
                    <h3 className="text-xl font-semibold text-text-primary mb-2">
                      Ready to Get Started?
                    </h3>
                    <p className="text-text-secondary mb-4">
                      Let&apos;s discuss how our {activeArea.title.toLowerCase()} expertise can transform your business.
                    </p>
                    <Button asChild className={`bg-${activeArea.color} text-white hover:bg-${activeArea.color}/90`}>
                      <Link href="/contact">
                        Start Your Project
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  )
}
