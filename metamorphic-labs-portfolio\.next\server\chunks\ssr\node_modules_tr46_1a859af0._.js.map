{"version": 3, "sources": [], "sections": [{"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/node_modules/tr46/index.js"], "sourcesContent": ["\"use strict\";\n\nvar punycode = require(\"punycode\");\nvar mappingTable = require(\"./lib/mappingTable.json\");\n\nvar PROCESSING_OPTIONS = {\n  TRANSITIONAL: 0,\n  NONTRANSITIONAL: 1\n};\n\nfunction normalize(str) { // fix bug in v8\n  return str.split('\\u0000').map(function (s) { return s.normalize('NFC'); }).join('\\u0000');\n}\n\nfunction findStatus(val) {\n  var start = 0;\n  var end = mappingTable.length - 1;\n\n  while (start <= end) {\n    var mid = Math.floor((start + end) / 2);\n\n    var target = mappingTable[mid];\n    if (target[0][0] <= val && target[0][1] >= val) {\n      return target;\n    } else if (target[0][0] > val) {\n      end = mid - 1;\n    } else {\n      start = mid + 1;\n    }\n  }\n\n  return null;\n}\n\nvar regexAstralSymbols = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n\nfunction countSymbols(string) {\n  return string\n    // replace every surrogate pair with a BMP symbol\n    .replace(regexAstralSymbols, '_')\n    // then get the length\n    .length;\n}\n\nfunction mapChars(domain_name, useSTD3, processing_option) {\n  var hasError = false;\n  var processed = \"\";\n\n  var len = countSymbols(domain_name);\n  for (var i = 0; i < len; ++i) {\n    var codePoint = domain_name.codePointAt(i);\n    var status = findStatus(codePoint);\n\n    switch (status[1]) {\n      case \"disallowed\":\n        hasError = true;\n        processed += String.fromCodePoint(codePoint);\n        break;\n      case \"ignored\":\n        break;\n      case \"mapped\":\n        processed += String.fromCodePoint.apply(String, status[2]);\n        break;\n      case \"deviation\":\n        if (processing_option === PROCESSING_OPTIONS.TRANSITIONAL) {\n          processed += String.fromCodePoint.apply(String, status[2]);\n        } else {\n          processed += String.fromCodePoint(codePoint);\n        }\n        break;\n      case \"valid\":\n        processed += String.fromCodePoint(codePoint);\n        break;\n      case \"disallowed_STD3_mapped\":\n        if (useSTD3) {\n          hasError = true;\n          processed += String.fromCodePoint(codePoint);\n        } else {\n          processed += String.fromCodePoint.apply(String, status[2]);\n        }\n        break;\n      case \"disallowed_STD3_valid\":\n        if (useSTD3) {\n          hasError = true;\n        }\n\n        processed += String.fromCodePoint(codePoint);\n        break;\n    }\n  }\n\n  return {\n    string: processed,\n    error: hasError\n  };\n}\n\nvar combiningMarksRegex = /[\\u0300-\\u036F\\u0483-\\u0489\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u0610-\\u061A\\u064B-\\u065F\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED\\u0711\\u0730-\\u074A\\u07A6-\\u07B0\\u07EB-\\u07F3\\u0816-\\u0819\\u081B-\\u0823\\u0825-\\u0827\\u0829-\\u082D\\u0859-\\u085B\\u08E4-\\u0903\\u093A-\\u093C\\u093E-\\u094F\\u0951-\\u0957\\u0962\\u0963\\u0981-\\u0983\\u09BC\\u09BE-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CD\\u09D7\\u09E2\\u09E3\\u0A01-\\u0A03\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A70\\u0A71\\u0A75\\u0A81-\\u0A83\\u0ABC\\u0ABE-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AE2\\u0AE3\\u0B01-\\u0B03\\u0B3C\\u0B3E-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B62\\u0B63\\u0B82\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD7\\u0C00-\\u0C03\\u0C3E-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C62\\u0C63\\u0C81-\\u0C83\\u0CBC\\u0CBE-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CE2\\u0CE3\\u0D01-\\u0D03\\u0D3E-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4D\\u0D57\\u0D62\\u0D63\\u0D82\\u0D83\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DF2\\u0DF3\\u0E31\\u0E34-\\u0E3A\\u0E47-\\u0E4E\\u0EB1\\u0EB4-\\u0EB9\\u0EBB\\u0EBC\\u0EC8-\\u0ECD\\u0F18\\u0F19\\u0F35\\u0F37\\u0F39\\u0F3E\\u0F3F\\u0F71-\\u0F84\\u0F86\\u0F87\\u0F8D-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u102B-\\u103E\\u1056-\\u1059\\u105E-\\u1060\\u1062-\\u1064\\u1067-\\u106D\\u1071-\\u1074\\u1082-\\u108D\\u108F\\u109A-\\u109D\\u135D-\\u135F\\u1712-\\u1714\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17B4-\\u17D3\\u17DD\\u180B-\\u180D\\u18A9\\u1920-\\u192B\\u1930-\\u193B\\u19B0-\\u19C0\\u19C8\\u19C9\\u1A17-\\u1A1B\\u1A55-\\u1A5E\\u1A60-\\u1A7C\\u1A7F\\u1AB0-\\u1ABE\\u1B00-\\u1B04\\u1B34-\\u1B44\\u1B6B-\\u1B73\\u1B80-\\u1B82\\u1BA1-\\u1BAD\\u1BE6-\\u1BF3\\u1C24-\\u1C37\\u1CD0-\\u1CD2\\u1CD4-\\u1CE8\\u1CED\\u1CF2-\\u1CF4\\u1CF8\\u1CF9\\u1DC0-\\u1DF5\\u1DFC-\\u1DFF\\u20D0-\\u20F0\\u2CEF-\\u2CF1\\u2D7F\\u2DE0-\\u2DFF\\u302A-\\u302F\\u3099\\u309A\\uA66F-\\uA672\\uA674-\\uA67D\\uA69F\\uA6F0\\uA6F1\\uA802\\uA806\\uA80B\\uA823-\\uA827\\uA880\\uA881\\uA8B4-\\uA8C4\\uA8E0-\\uA8F1\\uA926-\\uA92D\\uA947-\\uA953\\uA980-\\uA983\\uA9B3-\\uA9C0\\uA9E5\\uAA29-\\uAA36\\uAA43\\uAA4C\\uAA4D\\uAA7B-\\uAA7D\\uAAB0\\uAAB2-\\uAAB4\\uAAB7\\uAAB8\\uAABE\\uAABF\\uAAC1\\uAAEB-\\uAAEF\\uAAF5\\uAAF6\\uABE3-\\uABEA\\uABEC\\uABED\\uFB1E\\uFE00-\\uFE0F\\uFE20-\\uFE2D]|\\uD800[\\uDDFD\\uDEE0\\uDF76-\\uDF7A]|\\uD802[\\uDE01-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE0F\\uDE38-\\uDE3A\\uDE3F\\uDEE5\\uDEE6]|\\uD804[\\uDC00-\\uDC02\\uDC38-\\uDC46\\uDC7F-\\uDC82\\uDCB0-\\uDCBA\\uDD00-\\uDD02\\uDD27-\\uDD34\\uDD73\\uDD80-\\uDD82\\uDDB3-\\uDDC0\\uDE2C-\\uDE37\\uDEDF-\\uDEEA\\uDF01-\\uDF03\\uDF3C\\uDF3E-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF57\\uDF62\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDCB0-\\uDCC3\\uDDAF-\\uDDB5\\uDDB8-\\uDDC0\\uDE30-\\uDE40\\uDEAB-\\uDEB7]|\\uD81A[\\uDEF0-\\uDEF4\\uDF30-\\uDF36]|\\uD81B[\\uDF51-\\uDF7E\\uDF8F-\\uDF92]|\\uD82F[\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD83A[\\uDCD0-\\uDCD6]|\\uDB40[\\uDD00-\\uDDEF]/;\n\nfunction validateLabel(label, processing_option) {\n  if (label.substr(0, 4) === \"xn--\") {\n    label = punycode.toUnicode(label);\n    processing_option = PROCESSING_OPTIONS.NONTRANSITIONAL;\n  }\n\n  var error = false;\n\n  if (normalize(label) !== label ||\n      (label[3] === \"-\" && label[4] === \"-\") ||\n      label[0] === \"-\" || label[label.length - 1] === \"-\" ||\n      label.indexOf(\".\") !== -1 ||\n      label.search(combiningMarksRegex) === 0) {\n    error = true;\n  }\n\n  var len = countSymbols(label);\n  for (var i = 0; i < len; ++i) {\n    var status = findStatus(label.codePointAt(i));\n    if ((processing === PROCESSING_OPTIONS.TRANSITIONAL && status[1] !== \"valid\") ||\n        (processing === PROCESSING_OPTIONS.NONTRANSITIONAL &&\n         status[1] !== \"valid\" && status[1] !== \"deviation\")) {\n      error = true;\n      break;\n    }\n  }\n\n  return {\n    label: label,\n    error: error\n  };\n}\n\nfunction processing(domain_name, useSTD3, processing_option) {\n  var result = mapChars(domain_name, useSTD3, processing_option);\n  result.string = normalize(result.string);\n\n  var labels = result.string.split(\".\");\n  for (var i = 0; i < labels.length; ++i) {\n    try {\n      var validation = validateLabel(labels[i]);\n      labels[i] = validation.label;\n      result.error = result.error || validation.error;\n    } catch(e) {\n      result.error = true;\n    }\n  }\n\n  return {\n    string: labels.join(\".\"),\n    error: result.error\n  };\n}\n\nmodule.exports.toASCII = function(domain_name, useSTD3, processing_option, verifyDnsLength) {\n  var result = processing(domain_name, useSTD3, processing_option);\n  var labels = result.string.split(\".\");\n  labels = labels.map(function(l) {\n    try {\n      return punycode.toASCII(l);\n    } catch(e) {\n      result.error = true;\n      return l;\n    }\n  });\n\n  if (verifyDnsLength) {\n    var total = labels.slice(0, labels.length - 1).join(\".\").length;\n    if (total.length > 253 || total.length === 0) {\n      result.error = true;\n    }\n\n    for (var i=0; i < labels.length; ++i) {\n      if (labels.length > 63 || labels.length === 0) {\n        result.error = true;\n        break;\n      }\n    }\n  }\n\n  if (result.error) return null;\n  return labels.join(\".\");\n};\n\nmodule.exports.toUnicode = function(domain_name, useSTD3) {\n  var result = processing(domain_name, useSTD3, PROCESSING_OPTIONS.NONTRANSITIONAL);\n\n  return {\n    domain: result.string,\n    error: result.error\n  };\n};\n\nmodule.exports.PROCESSING_OPTIONS = PROCESSING_OPTIONS;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,IAAI;AAEJ,IAAI,qBAAqB;IACvB,cAAc;IACd,iBAAiB;AACnB;AAEA,SAAS,UAAU,GAAG;IACpB,OAAO,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,SAAU,CAAC;QAAI,OAAO,EAAE,SAAS,CAAC;IAAQ,GAAG,IAAI,CAAC;AACnF;AAEA,SAAS,WAAW,GAAG;IACrB,IAAI,QAAQ;IACZ,IAAI,MAAM,aAAa,MAAM,GAAG;IAEhC,MAAO,SAAS,IAAK;QACnB,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,QAAQ,GAAG,IAAI;QAErC,IAAI,SAAS,YAAY,CAAC,IAAI;QAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK;YAC9C,OAAO;QACT,OAAO,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK;YAC7B,MAAM,MAAM;QACd,OAAO;YACL,QAAQ,MAAM;QAChB;IACF;IAEA,OAAO;AACT;AAEA,IAAI,qBAAqB;AAEzB,SAAS,aAAa,MAAM;IAC1B,OAAO,MACL,iDAAiD;KAChD,OAAO,CAAC,oBAAoB,IAC7B,sBAAsB;KACrB,MAAM;AACX;AAEA,SAAS,SAAS,WAAW,EAAE,OAAO,EAAE,iBAAiB;IACvD,IAAI,WAAW;IACf,IAAI,YAAY;IAEhB,IAAI,MAAM,aAAa;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC5B,IAAI,YAAY,YAAY,WAAW,CAAC;QACxC,IAAI,SAAS,WAAW;QAExB,OAAQ,MAAM,CAAC,EAAE;YACf,KAAK;gBACH,WAAW;gBACX,aAAa,OAAO,aAAa,CAAC;gBAClC;YACF,KAAK;gBACH;YACF,KAAK;gBACH,aAAa,OAAO,aAAa,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,EAAE;gBACzD;YACF,KAAK;gBACH,IAAI,sBAAsB,mBAAmB,YAAY,EAAE;oBACzD,aAAa,OAAO,aAAa,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,EAAE;gBAC3D,OAAO;oBACL,aAAa,OAAO,aAAa,CAAC;gBACpC;gBACA;YACF,KAAK;gBACH,aAAa,OAAO,aAAa,CAAC;gBAClC;YACF,KAAK;gBACH,IAAI,SAAS;oBACX,WAAW;oBACX,aAAa,OAAO,aAAa,CAAC;gBACpC,OAAO;oBACL,aAAa,OAAO,aAAa,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,EAAE;gBAC3D;gBACA;YACF,KAAK;gBACH,IAAI,SAAS;oBACX,WAAW;gBACb;gBAEA,aAAa,OAAO,aAAa,CAAC;gBAClC;QACJ;IACF;IAEA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,IAAI,sBAAsB;AAE1B,SAAS,cAAc,KAAK,EAAE,iBAAiB;IAC7C,IAAI,MAAM,MAAM,CAAC,GAAG,OAAO,QAAQ;QACjC,QAAQ,SAAS,SAAS,CAAC;QAC3B,oBAAoB,mBAAmB,eAAe;IACxD;IAEA,IAAI,QAAQ;IAEZ,IAAI,UAAU,WAAW,SACpB,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,OAClC,KAAK,CAAC,EAAE,KAAK,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,KAAK,OAChD,MAAM,OAAO,CAAC,SAAS,CAAC,KACxB,MAAM,MAAM,CAAC,yBAAyB,GAAG;QAC3C,QAAQ;IACV;IAEA,IAAI,MAAM,aAAa;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC5B,IAAI,SAAS,WAAW,MAAM,WAAW,CAAC;QAC1C,IAAI,AAAC,eAAe,mBAAmB,YAAY,IAAI,MAAM,CAAC,EAAE,KAAK,WAChE,eAAe,mBAAmB,eAAe,IACjD,MAAM,CAAC,EAAE,KAAK,WAAW,MAAM,CAAC,EAAE,KAAK,aAAc;YACxD,QAAQ;YACR;QACF;IACF;IAEA,OAAO;QACL,OAAO;QACP,OAAO;IACT;AACF;AAEA,SAAS,WAAW,WAAW,EAAE,OAAO,EAAE,iBAAiB;IACzD,IAAI,SAAS,SAAS,aAAa,SAAS;IAC5C,OAAO,MAAM,GAAG,UAAU,OAAO,MAAM;IAEvC,IAAI,SAAS,OAAO,MAAM,CAAC,KAAK,CAAC;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACtC,IAAI;YACF,IAAI,aAAa,cAAc,MAAM,CAAC,EAAE;YACxC,MAAM,CAAC,EAAE,GAAG,WAAW,KAAK;YAC5B,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI,WAAW,KAAK;QACjD,EAAE,OAAM,GAAG;YACT,OAAO,KAAK,GAAG;QACjB;IACF;IAEA,OAAO;QACL,QAAQ,OAAO,IAAI,CAAC;QACpB,OAAO,OAAO,KAAK;IACrB;AACF;AAEA,OAAO,OAAO,CAAC,OAAO,GAAG,SAAS,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE,eAAe;IACxF,IAAI,SAAS,WAAW,aAAa,SAAS;IAC9C,IAAI,SAAS,OAAO,MAAM,CAAC,KAAK,CAAC;IACjC,SAAS,OAAO,GAAG,CAAC,SAAS,CAAC;QAC5B,IAAI;YACF,OAAO,SAAS,OAAO,CAAC;QAC1B,EAAE,OAAM,GAAG;YACT,OAAO,KAAK,GAAG;YACf,OAAO;QACT;IACF;IAEA,IAAI,iBAAiB;QACnB,IAAI,QAAQ,OAAO,KAAK,CAAC,GAAG,OAAO,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,MAAM;QAC/D,IAAI,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,KAAK,GAAG;YAC5C,OAAO,KAAK,GAAG;QACjB;QAEA,IAAK,IAAI,IAAE,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACpC,IAAI,OAAO,MAAM,GAAG,MAAM,OAAO,MAAM,KAAK,GAAG;gBAC7C,OAAO,KAAK,GAAG;gBACf;YACF;QACF;IACF;IAEA,IAAI,OAAO,KAAK,EAAE,OAAO;IACzB,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA,OAAO,OAAO,CAAC,SAAS,GAAG,SAAS,WAAW,EAAE,OAAO;IACtD,IAAI,SAAS,WAAW,aAAa,SAAS,mBAAmB,eAAe;IAEhF,OAAO;QACL,QAAQ,OAAO,MAAM;QACrB,OAAO,OAAO,KAAK;IACrB;AACF;AAEA,OAAO,OAAO,CAAC,kBAAkB,GAAG", "ignoreList": [0], "debugId": null}}]}