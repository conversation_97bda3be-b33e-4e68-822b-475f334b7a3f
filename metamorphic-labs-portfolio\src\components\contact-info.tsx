'use client'

import { motion } from 'framer-motion'
import { Mail, MapPin, Clock, Calendar, Phone, MessageSquare } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

const contactMethods = [
  {
    icon: Mail,
    title: 'Email Us',
    description: 'Send us an email and we\'ll respond within 24 hours',
    value: '<EMAIL>',
    action: 'mailto:<EMAIL>',
    color: 'secondary'
  },
  {
    icon: Phone,
    title: 'Schedule a Call',
    description: 'Book a free 30-minute consultation call',
    value: 'Book Meeting',
    action: 'https://calendly.com/metamorphiclabs/consultation',
    color: 'tertiary'
  },
  {
    icon: MessageSquare,
    title: 'Live Chat',
    description: 'Chat with our team during business hours',
    value: 'Start Chat',
    action: '#',
    color: 'accent'
  }
]

const officeInfo = [
  {
    icon: MapPin,
    title: 'Headquarters',
    value: 'San Francisco, CA\nUnited States'
  },
  {
    icon: Clock,
    title: 'Business Hours',
    value: 'Monday - Friday\n9:00 AM - 6:00 PM PST'
  },
  {
    icon: Calendar,
    title: 'Response Time',
    value: 'Within 24 hours\nUsually much sooner'
  }
]

const faqs = [
  {
    question: 'How long does a typical project take?',
    answer: 'Project timelines vary based on complexity, but most projects range from 3-12 months. We provide detailed timelines during our initial consultation.'
  },
  {
    question: 'Do you work with startups?',
    answer: 'Absolutely! We work with companies of all sizes, from early-stage startups to Fortune 500 enterprises.'
  },
  {
    question: 'What technologies do you specialize in?',
    answer: 'We specialize in AI/ML technologies, modern web frameworks (React, Next.js), cloud platforms (AWS, GCP, Azure), and enterprise software development.'
  },
  {
    question: 'Do you provide ongoing support?',
    answer: 'Yes, we offer comprehensive support and maintenance packages to ensure your solutions continue to perform optimally.'
  }
]

export function ContactInfo() {
  return (
    <div className="space-y-8">
      {/* Contact Methods */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-2xl font-bold text-text-primary mb-6">
          Other Ways to Reach Us
        </h2>
        <div className="space-y-4">
          {contactMethods.map((method, index) => (
            <motion.div
              key={method.title}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`flex h-12 w-12 items-center justify-center rounded-full bg-${method.color}/10 flex-shrink-0`}>
                      <method.icon className={`h-6 w-6 text-${method.color}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-text-primary mb-1">
                        {method.title}
                      </h3>
                      <p className="text-text-secondary text-sm mb-3">
                        {method.description}
                      </p>
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className={`border-${method.color}/20 text-${method.color} hover:bg-${method.color}/10`}
                      >
                        <a
                          href={method.action}
                          target={method.action.startsWith('http') ? '_blank' : undefined}
                          rel={method.action.startsWith('http') ? 'noopener noreferrer' : undefined}
                        >
                          {method.value}
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Office Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="bg-surface-alt border-secondary/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-text-primary">
              Office Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-6">
              {officeInfo.map((info) => (
                <div key={info.title} className="flex items-start space-x-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-secondary/10 flex-shrink-0">
                    <info.icon className="h-5 w-5 text-secondary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-text-primary mb-1">
                      {info.title}
                    </h4>
                    <p className="text-text-secondary text-sm whitespace-pre-line">
                      {info.value}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <Card className="bg-surface-alt border-secondary/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-text-primary">
              Frequently Asked Questions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-6">
              {faqs.map((faq, faqIndex) => (
                <div key={faqIndex}>
                  <h4 className="font-medium text-text-primary mb-2">
                    {faq.question}
                  </h4>
                  <p className="text-text-secondary text-sm leading-relaxed">
                    {faq.answer}
                  </p>
                  {faqIndex < faqs.length - 1 && (
                    <hr className="mt-6 border-secondary/20" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Emergency Contact */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.9 }}
      >
        <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-text-primary mb-2">
              Need Urgent Support?
            </h3>
            <p className="text-text-secondary text-sm mb-4">
              For existing clients with critical issues, we offer 24/7 emergency support.
            </p>
            <Button
              asChild
              className="bg-accent text-surface hover:bg-accent/90"
            >
              <a href="tel:******-METAMORPHIC">
                Call Emergency Line
              </a>
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
