'use client'

import { motion } from 'framer-motion'
import { Brain, Cog, Code } from 'lucide-react'

export function ExpertiseHero() {
  return (
    <section className="relative py-24 sm:py-32 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-secondary/10 via-tertiary/5 to-accent/10" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl lg:text-7xl">
              Our{' '}
              <span className="bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent">
                Expertise
              </span>
            </h1>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mt-6 text-lg leading-8 text-text-secondary sm:text-xl lg:text-2xl max-w-3xl mx-auto"
          >
            We specialize in three core areas that drive digital transformation: 
            AI platforms that adapt and evolve, intelligent automation that scales, 
            and custom software that delivers exceptional results.
          </motion.p>
          
          {/* Core Expertise Areas */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3"
          >
            <div className="flex flex-col items-center text-center">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-secondary/10 mb-6">
                <Brain className="h-10 w-10 text-secondary" />
              </div>
              <h3 className="text-xl font-semibold text-text-primary mb-3">AI Platforms</h3>
              <p className="text-text-secondary text-sm leading-relaxed">
                Adaptive AI systems that learn, evolve, and optimize performance across 
                multiple models and use cases.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-tertiary/10 mb-6">
                <Cog className="h-10 w-10 text-tertiary" />
              </div>
              <h3 className="text-xl font-semibold text-text-primary mb-3">Intelligent Automation</h3>
              <p className="text-text-secondary text-sm leading-relaxed">
                Self-optimizing systems that automate complex workflows while 
                continuously improving efficiency and accuracy.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-accent/10 mb-6">
                <Code className="h-10 w-10 text-accent" />
              </div>
              <h3 className="text-xl font-semibold text-text-primary mb-3">Custom Software</h3>
              <p className="text-text-secondary text-sm leading-relaxed">
                Bespoke solutions built with cutting-edge technologies to solve 
                unique business challenges and drive growth.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
