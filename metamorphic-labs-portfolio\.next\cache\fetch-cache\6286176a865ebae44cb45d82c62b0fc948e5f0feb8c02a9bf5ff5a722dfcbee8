{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e5426a92fe02b-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?select=%2A&slug=eq.metamorphic-ai-platform", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:59:53 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=N78GEY1ItBdGsCBR5zpzFbgdkYxW5Z8zO.ajJkCz.wQ-1750622393-*******-VYmKZ00JlRvnLoOfDprn9gDThVpTQkxLYVmKGvJ7wvrGWzgGEXsqnU3jIMrVqIHRa0M4cctzYYZK0qSXwK1daDQzykMBfIMP8N8_ibcSLfI; path=/; expires=Sun, 22-Jun-25 20:29:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "1"}, "body": "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", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&slug=eq.metamorphic-ai-platform"}, "revalidate": 31536000, "tags": []}