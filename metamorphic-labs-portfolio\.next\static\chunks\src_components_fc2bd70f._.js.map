{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/expertise-hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Brain, Cog, Code } from 'lucide-react'\n\nexport function ExpertiseHero() {\n  return (\n    <section className=\"relative py-24 sm:py-32 overflow-hidden\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-secondary/10 via-tertiary/5 to-accent/10\" />\n      \n      <div className=\"relative mx-auto max-w-7xl px-6 lg:px-8\">\n        <div className=\"mx-auto max-w-4xl text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h1 className=\"text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl lg:text-7xl\">\n              Our{' '}\n              <span className=\"bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent\">\n                Expertise\n              </span>\n            </h1>\n          </motion.div>\n          \n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"mt-6 text-lg leading-8 text-text-secondary sm:text-xl lg:text-2xl max-w-3xl mx-auto\"\n          >\n            We specialize in three core areas that drive digital transformation: \n            AI platforms that adapt and evolve, intelligent automation that scales, \n            and custom software that delivers exceptional results.\n          </motion.p>\n          \n          {/* Core Expertise Areas */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3\"\n          >\n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-20 w-20 items-center justify-center rounded-full bg-secondary/10 mb-6\">\n                <Brain className=\"h-10 w-10 text-secondary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-text-primary mb-3\">AI Platforms</h3>\n              <p className=\"text-text-secondary text-sm leading-relaxed\">\n                Adaptive AI systems that learn, evolve, and optimize performance across \n                multiple models and use cases.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-20 w-20 items-center justify-center rounded-full bg-tertiary/10 mb-6\">\n                <Cog className=\"h-10 w-10 text-tertiary\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-text-primary mb-3\">Intelligent Automation</h3>\n              <p className=\"text-text-secondary text-sm leading-relaxed\">\n                Self-optimizing systems that automate complex workflows while \n                continuously improving efficiency and accuracy.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-20 w-20 items-center justify-center rounded-full bg-accent/10 mb-6\">\n                <Code className=\"h-10 w-10 text-accent\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-text-primary mb-3\">Custom Software</h3>\n              <p className=\"text-text-secondary text-sm leading-relaxed\">\n                Bespoke solutions built with cutting-edge technologies to solve \n                unique business challenges and drive growth.\n              </p>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAG,WAAU;;oCAAmF;oCAC3F;kDACJ,6LAAC;wCAAK,WAAU;kDAAuF;;;;;;;;;;;;;;;;;sCAM3G,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAOD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;8CAM7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;8CAM7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzE;KA5EgB", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/expertise-tabs.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Brain, Cog, Code, CheckCircle, ArrowRight } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport Link from 'next/link'\n\nconst expertiseAreas = [\n  {\n    id: 'ai-platforms',\n    title: 'AI Platforms',\n    icon: Brain,\n    color: 'secondary',\n    description: 'Adaptive AI systems that learn, evolve, and optimize performance across multiple models and use cases.',\n    capabilities: [\n      'Multi-Model AI Integration',\n      'Adaptive Learning Systems',\n      'Real-time Performance Optimization',\n      'Intelligent Routing & Load Balancing',\n      'Custom Model Training & Fine-tuning',\n      'Enterprise-Grade Security & Compliance'\n    ],\n    technologies: ['OpenAI GPT-4', 'Anthropic Claude', 'Google Gemini', 'Hugging Face', 'TensorFlow', 'PyTorch'],\n    projects: ['Metamorphic AI Platform', 'AI Integration & Broker'],\n    benefits: [\n      '40% reduction in AI implementation time',\n      '60% improvement in model performance',\n      '99.9% uptime with intelligent failover',\n      '50% cost savings through optimization'\n    ]\n  },\n  {\n    id: 'intelligent-automation',\n    title: 'Intelligent Automation',\n    icon: Cog,\n    color: 'tertiary',\n    description: 'Self-optimizing systems that automate complex workflows while continuously improving efficiency and accuracy.',\n    capabilities: [\n      'Self-Optimizing CI/CD Pipelines',\n      'Predictive Anomaly Detection',\n      'Automated Quality Assurance',\n      'Dynamic Resource Management',\n      'Intelligent Process Orchestration',\n      'Real-time Performance Analytics'\n    ],\n    technologies: ['Kubernetes', 'Docker', 'Jenkins', 'GitLab CI', 'Prometheus', 'Grafana'],\n    projects: ['Living Pipeline', 'Metamorphic Testing'],\n    benefits: [\n      '70% reduction in deployment failures',\n      '85% faster build and test cycles',\n      '60% decrease in manual intervention',\n      '90% improvement in resource utilization'\n    ]\n  },\n  {\n    id: 'custom-software',\n    title: 'Custom Software',\n    icon: Code,\n    color: 'accent',\n    description: 'Bespoke solutions built with cutting-edge technologies to solve unique business challenges and drive growth.',\n    capabilities: [\n      'Full-Stack Web Applications',\n      'Mobile App Development',\n      'Cloud-Native Architecture',\n      'Microservices Design',\n      'API Development & Integration',\n      'Database Design & Optimization'\n    ],\n    technologies: ['React', 'Next.js', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS/GCP/Azure'],\n    projects: ['Metamorphic SaaS Suite', 'Metamorphic Reactor'],\n    benefits: [\n      '3x faster development cycles',\n      '99.9% application uptime',\n      '50% reduction in maintenance costs',\n      '100% client satisfaction rate'\n    ]\n  }\n]\n\nexport function ExpertiseTabs() {\n  const [activeTab, setActiveTab] = useState('ai-platforms')\n  const activeArea = expertiseAreas.find(area => area.id === activeTab)!\n\n  return (\n    <section className=\"py-24 sm:py-32\">\n      <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n        {/* Tab Navigation */}\n        <div className=\"flex flex-col sm:flex-row justify-center mb-12\">\n          <div className=\"flex flex-col sm:flex-row bg-surface-alt rounded-2xl p-2 gap-2\">\n            {expertiseAreas.map((area) => {\n              const Icon = area.icon\n              return (\n                <button\n                  key={area.id}\n                  onClick={() => setActiveTab(area.id)}\n                  className={`flex items-center gap-3 px-6 py-4 rounded-xl transition-all duration-300 ${\n                    activeTab === area.id\n                      ? `bg-${area.color} text-white shadow-lg`\n                      : 'text-text-secondary hover:text-text-primary hover:bg-surface'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">{area.title}</span>\n                </button>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\n              {/* Left Column - Overview */}\n              <div className=\"space-y-8\">\n                <div>\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <div className={`flex h-12 w-12 items-center justify-center rounded-full bg-${activeArea.color}/10`}>\n                      <activeArea.icon className={`h-6 w-6 text-${activeArea.color}`} />\n                    </div>\n                    <h2 className=\"text-3xl font-bold text-text-primary\">\n                      {activeArea.title}\n                    </h2>\n                  </div>\n                  <p className=\"text-lg text-text-secondary leading-relaxed\">\n                    {activeArea.description}\n                  </p>\n                </div>\n\n                {/* Capabilities */}\n                <Card className=\"bg-surface-alt border-secondary/20\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                      Core Capabilities\n                    </h3>\n                    <div className=\"grid grid-cols-1 gap-3\">\n                      {activeArea.capabilities.map((capability, index) => (\n                        <motion.div\n                          key={capability}\n                          initial={{ opacity: 0, x: -20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          transition={{ duration: 0.3, delay: index * 0.1 }}\n                          className=\"flex items-center gap-3\"\n                        >\n                          <CheckCircle className={`h-5 w-5 text-${activeArea.color} flex-shrink-0`} />\n                          <span className=\"text-text-secondary\">{capability}</span>\n                        </motion.div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Technologies */}\n                <div>\n                  <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                    Technologies & Tools\n                  </h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {activeArea.technologies.map((tech) => (\n                      <Badge\n                        key={tech}\n                        variant=\"secondary\"\n                        className={`bg-${activeArea.color}/10 text-${activeArea.color} border-${activeArea.color}/20`}\n                      >\n                        {tech}\n                      </Badge>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Right Column - Benefits & Projects */}\n              <div className=\"space-y-8\">\n                {/* Key Benefits */}\n                <Card className=\"bg-surface-alt border-secondary/20\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                      Proven Results\n                    </h3>\n                    <div className=\"grid grid-cols-1 gap-4\">\n                      {activeArea.benefits.map((benefit, index) => (\n                        <motion.div\n                          key={benefit}\n                          initial={{ opacity: 0, scale: 0.95 }}\n                          animate={{ opacity: 1, scale: 1 }}\n                          transition={{ duration: 0.3, delay: index * 0.1 }}\n                          className={`p-4 rounded-lg bg-${activeArea.color}/5 border border-${activeArea.color}/20`}\n                        >\n                          <span className=\"text-text-primary font-medium\">{benefit}</span>\n                        </motion.div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Related Projects */}\n                <Card className=\"bg-surface-alt border-secondary/20\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n                      Featured Projects\n                    </h3>\n                    <div className=\"space-y-3\">\n                      {activeArea.projects.map((project) => (\n                        <div\n                          key={project}\n                          className=\"flex items-center justify-between p-3 rounded-lg bg-surface hover:bg-surface/80 transition-colors\"\n                        >\n                          <span className=\"text-text-primary font-medium\">{project}</span>\n                          <ArrowRight className=\"h-4 w-4 text-text-secondary\" />\n                        </div>\n                      ))}\n                    </div>\n                    <div className=\"mt-6\">\n                      <Button asChild variant=\"outline\" className=\"w-full\">\n                        <Link href=\"/projects\">\n                          View All Projects\n                        </Link>\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* CTA */}\n                <Card className={`bg-gradient-to-br from-${activeArea.color}/10 to-${activeArea.color}/5 border-${activeArea.color}/20`}>\n                  <CardContent className=\"p-6 text-center\">\n                    <h3 className=\"text-xl font-semibold text-text-primary mb-2\">\n                      Ready to Get Started?\n                    </h3>\n                    <p className=\"text-text-secondary mb-4\">\n                      Let&apos;s discuss how our {activeArea.title.toLowerCase()} expertise can transform your business.\n                    </p>\n                    <Button asChild className={`bg-${activeArea.color} text-white hover:bg-${activeArea.color}/90`}>\n                      <Link href=\"/contact\">\n                        Start Your Project\n                      </Link>\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAgB;YAAoB;YAAiB;YAAgB;YAAc;SAAU;QAC5G,UAAU;YAAC;YAA2B;SAA0B;QAChE,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAc;YAAU;YAAW;YAAa;YAAc;SAAU;QACvF,UAAU;YAAC;YAAmB;SAAsB;QACpD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAW;YAAc;YAAc;SAAgB;QAC1F,UAAU;YAAC;YAA0B;SAAsB;QAC3D,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE3D,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC;4BACnB,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,6LAAC;gCAEC,SAAS,IAAM,aAAa,KAAK,EAAE;gCACnC,WAAW,CAAC,yEAAyE,EACnF,cAAc,KAAK,EAAE,GACjB,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,qBAAqB,CAAC,GACvC,gEACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAe,KAAK,KAAK;;;;;;;+BATpC,KAAK,EAAE;;;;;wBAYlB;;;;;;;;;;;8BAKJ,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,2DAA2D,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC;sEACjG,cAAA,6LAAC,WAAW,IAAI;gEAAC,WAAW,CAAC,aAAa,EAAE,WAAW,KAAK,EAAE;;;;;;;;;;;sEAEhE,6LAAC;4DAAG,WAAU;sEACX,WAAW,KAAK;;;;;;;;;;;;8DAGrB,6LAAC;oDAAE,WAAU;8DACV,WAAW,WAAW;;;;;;;;;;;;sDAK3B,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAI,WAAU;kEACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,sBACxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,UAAU;oEAAK,OAAO,QAAQ;gEAAI;gEAChD,WAAU;;kFAEV,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAW,CAAC,aAAa,EAAE,WAAW,KAAK,CAAC,cAAc,CAAC;;;;;;kFACxE,6LAAC;wEAAK,WAAU;kFAAuB;;;;;;;+DAPlC;;;;;;;;;;;;;;;;;;;;;sDAef,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAG7D,6LAAC;oDAAI,WAAU;8DACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,6LAAC,oIAAA,CAAA,QAAK;4DAEJ,SAAQ;4DACR,WAAW,CAAC,GAAG,EAAE,WAAW,KAAK,CAAC,SAAS,EAAE,WAAW,KAAK,CAAC,QAAQ,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC;sEAE5F;2DAJI;;;;;;;;;;;;;;;;;;;;;;8CAYf,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAK;gEACnC,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAE;gEAChC,YAAY;oEAAE,UAAU;oEAAK,OAAO,QAAQ;gEAAI;gEAChD,WAAW,CAAC,kBAAkB,EAAE,WAAW,KAAK,CAAC,iBAAiB,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC;0EAEzF,cAAA,6LAAC;oEAAK,WAAU;8EAAiC;;;;;;+DAN5C;;;;;;;;;;;;;;;;;;;;;sDAcf,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACxB,6LAAC;gEAEC,WAAU;;kFAEV,6LAAC;wEAAK,WAAU;kFAAiC;;;;;;kFACjD,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;+DAJjB;;;;;;;;;;kEAQX,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO;4DAAC,SAAQ;4DAAU,WAAU;sEAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;0EAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAS/B,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAW,CAAC,uBAAuB,EAAE,WAAW,KAAK,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,UAAU,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC;sDACrH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAE,WAAU;;4DAA2B;4DACV,WAAW,KAAK,CAAC,WAAW;4DAAG;;;;;;;kEAE7D,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAC,WAAW,CAAC,GAAG,EAAE,WAAW,KAAK,CAAC,qBAAqB,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC;kEAC5F,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA9H3B;;;;;;;;;;;;;;;;;;;;;AA2IjB;GA5KgB;KAAA", "debugId": null}}]}