{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/mdx-content.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo } from 'react'\nimport ReactMarkdown from 'react-markdown'\nimport { Prism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-syntax-highlighter'\nimport { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'\n\ninterface MDXContentProps {\n  content: string\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype ComponentProps = any\n\nexport function MDXContent({ content }: MDXContentProps) {\n  const components = useMemo(() => ({\n    h1: ({ children }: ComponentProps) => (\n      <h1 className=\"text-3xl font-bold text-text-primary mb-6 mt-8 first:mt-0\">\n        {children}\n      </h1>\n    ),\n    h2: ({ children }: ComponentProps) => (\n      <h2 className=\"text-2xl font-semibold text-text-primary mb-4 mt-8 first:mt-0\">\n        {children}\n      </h2>\n    ),\n    h3: ({ children }: ComponentProps) => (\n      <h3 className=\"text-xl font-semibold text-text-primary mb-3 mt-6\">\n        {children}\n      </h3>\n    ),\n    h4: ({ children }: ComponentProps) => (\n      <h4 className=\"text-lg font-semibold text-text-primary mb-2 mt-4\">\n        {children}\n      </h4>\n    ),\n    p: ({ children }: ComponentProps) => (\n      <p className=\"text-text-secondary leading-7 mb-4\">\n        {children}\n      </p>\n    ),\n    ul: ({ children }: ComponentProps) => (\n      <ul className=\"list-disc list-inside text-text-secondary mb-4 space-y-2\">\n        {children}\n      </ul>\n    ),\n    ol: ({ children }: ComponentProps) => (\n      <ol className=\"list-decimal list-inside text-text-secondary mb-4 space-y-2\">\n        {children}\n      </ol>\n    ),\n    li: ({ children }: ComponentProps) => (\n      <li className=\"leading-7\">\n        {children}\n      </li>\n    ),\n    strong: ({ children }: ComponentProps) => (\n      <strong className=\"font-semibold text-text-primary\">\n        {children}\n      </strong>\n    ),\n    em: ({ children }: ComponentProps) => (\n      <em className=\"italic text-text-primary\">\n        {children}\n      </em>\n    ),\n    code: ({ inline, className, children, ...props }: ComponentProps) => {\n      const match = /language-(\\w+)/.exec(className || '')\n      return !inline && match ? (\n        <div className=\"my-6\">\n          <SyntaxHighlighter\n            style={oneDark}\n            language={match[1]}\n            PreTag=\"div\"\n            className=\"rounded-lg\"\n            {...props}\n          >\n            {String(children).replace(/\\n$/, '')}\n          </SyntaxHighlighter>\n        </div>\n      ) : (\n        <code className=\"bg-tertiary/10 text-tertiary px-1.5 py-0.5 rounded text-sm font-mono\" {...props}>\n          {children}\n        </code>\n      )\n    },\n    blockquote: ({ children }: ComponentProps) => (\n      <blockquote className=\"border-l-4 border-secondary pl-4 my-6 italic text-text-secondary\">\n        {children}\n      </blockquote>\n    ),\n    a: ({ href, children }: ComponentProps) => (\n      <a\n        href={href}\n        className=\"text-secondary hover:text-secondary/80 underline underline-offset-2 transition-colors\"\n        target={href?.startsWith('http') ? '_blank' : undefined}\n        rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}\n      >\n        {children}\n      </a>\n    ),\n    hr: () => (\n      <hr className=\"border-secondary/20 my-8\" />\n    ),\n    table: ({ children }: ComponentProps) => (\n      <div className=\"overflow-x-auto my-6\">\n        <table className=\"min-w-full border border-secondary/20 rounded-lg\">\n          {children}\n        </table>\n      </div>\n    ),\n    thead: ({ children }: ComponentProps) => (\n      <thead className=\"bg-secondary/5\">\n        {children}\n      </thead>\n    ),\n    th: ({ children }: ComponentProps) => (\n      <th className=\"px-4 py-2 text-left font-semibold text-text-primary border-b border-secondary/20\">\n        {children}\n      </th>\n    ),\n    td: ({ children }: ComponentProps) => (\n      <td className=\"px-4 py-2 text-text-secondary border-b border-secondary/10\">\n        {children}\n      </td>\n    ),\n  }), [])\n\n  return (\n    <div className=\"prose prose-lg max-w-none\">\n      <ReactMarkdown components={components}>\n        {content}\n      </ReactMarkdown>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcO,SAAS,WAAW,EAAE,OAAO,EAAmB;;IACrD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE,IAAM,CAAC;gBAChC,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,CAAC;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC9B,6LAAC;4BAAE,WAAU;sCACV;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,MAAM;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBACnC,6LAAC;4BAAO,WAAU;sCACf;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,IAAI;sDAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAuB;wBAC9D,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;wBACjD,OAAO,CAAC,UAAU,sBAChB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,QAAiB;gCAChB,OAAO,wOAAA,CAAA,UAAO;gCACd,UAAU,KAAK,CAAC,EAAE;gCAClB,QAAO;gCACP,WAAU;gCACT,GAAG,KAAK;0CAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;iDAIrC,6LAAC;4BAAK,WAAU;4BAAwE,GAAG,KAAK;sCAC7F;;;;;;oBAGP;;gBACA,UAAU;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBACvC,6LAAC;4BAAW,WAAU;sCACnB;;;;;;;gBAGL,CAAC;sDAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAkB,iBACpC,6LAAC;4BACC,MAAM;4BACN,WAAU;4BACV,QAAQ,MAAM,WAAW,UAAU,WAAW;4BAC9C,KAAK,MAAM,WAAW,UAAU,wBAAwB;sCAEvD;;;;;;;gBAGL,EAAE;sDAAE,kBACF,6LAAC;4BAAG,WAAU;;;;;;;gBAEhB,KAAK;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAClC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;0CACd;;;;;;;;;;;;gBAIP,KAAK;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAClC,6LAAC;4BAAM,WAAU;sCACd;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;gBAGL,EAAE;sDAAE,CAAC,EAAE,QAAQ,EAAkB,iBAC/B,6LAAC;4BAAG,WAAU;sCACX;;;;;;;YAGP,CAAC;yCAAG,EAAE;IAEN,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;YAAC,YAAY;sBACxB;;;;;;;;;;;AAIT;GAzHgB;KAAA", "debugId": null}}]}