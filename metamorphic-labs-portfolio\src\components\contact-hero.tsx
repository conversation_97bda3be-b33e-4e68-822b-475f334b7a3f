'use client'

import { motion } from 'framer-motion'
import { MessageSquare, Clock, Shield } from 'lucide-react'

export function ContactHero() {
  return (
    <section className="relative py-24 sm:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-accent/10 via-secondary/5 to-tertiary/10" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl lg:text-7xl">
              Let&apos;s{' '}
              <span className="bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent">
                Build Together
              </span>
            </h1>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mt-6 text-lg leading-8 text-text-secondary sm:text-xl lg:text-2xl max-w-3xl mx-auto"
          >
            Ready to transform your ideas into intelligent solutions? We&apos;re here to help you 
            navigate the world of AI and custom software development.
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3"
          >
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-secondary/10 mb-4">
                <MessageSquare className="h-8 w-8 text-secondary" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Free Consultation</h3>
              <p className="text-text-secondary text-sm">
                Get expert advice on your project with no commitment required.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-tertiary/10 mb-4">
                <Clock className="h-8 w-8 text-tertiary" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Quick Response</h3>
              <p className="text-text-secondary text-sm">
                We respond to all inquiries within 24 hours, usually much sooner.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-accent/10 mb-4">
                <Shield className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">NDA Protection</h3>
              <p className="text-text-secondary text-sm">
                Your ideas are safe with us. We offer NDA protection for all discussions.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
