[{"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\expertise\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\about-hero.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-form.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-hero.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-info.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\expertise-hero.tsx": "12", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\expertise-tabs.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx": "16", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mission-section.tsx": "17", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx": "18", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx": "19", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx": "20", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\team-section.tsx": "21", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx": "22", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\timeline-section.tsx": "23", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx": "24", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx": "25", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx": "26", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts": "27", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts": "28", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts": "29"}, {"size": 645, "mtime": 1750621731034, "results": "30", "hashOfConfig": "31"}, {"size": 821, "mtime": 1750621996692, "results": "32", "hashOfConfig": "31"}, {"size": 486, "mtime": 1750621863154, "results": "33", "hashOfConfig": "31"}, {"size": 2350, "mtime": 1750618990956, "results": "34", "hashOfConfig": "31"}, {"size": 177, "mtime": 1750619049605, "results": "35", "hashOfConfig": "31"}, {"size": 1544, "mtime": 1750619294721, "results": "36", "hashOfConfig": "31"}, {"size": 5625, "mtime": 1750619523144, "results": "37", "hashOfConfig": "31"}, {"size": 3501, "mtime": 1750621608106, "results": "38", "hashOfConfig": "31"}, {"size": 15549, "mtime": 1750622263002, "results": "39", "hashOfConfig": "31"}, {"size": 3420, "mtime": 1750622207943, "results": "40", "hashOfConfig": "31"}, {"size": 7797, "mtime": 1750622343241, "results": "41", "hashOfConfig": "31"}, {"size": 3752, "mtime": 1750621881382, "results": "42", "hashOfConfig": "31"}, {"size": 10668, "mtime": 1750621920126, "results": "43", "hashOfConfig": "31"}, {"size": 3023, "mtime": 1750618935221, "results": "44", "hashOfConfig": "31"}, {"size": 5113, "mtime": 1750619079034, "results": "45", "hashOfConfig": "31"}, {"size": 4133, "mtime": 1750621515539, "results": "46", "hashOfConfig": "31"}, {"size": 4298, "mtime": 1750621627175, "results": "47", "hashOfConfig": "31"}, {"size": 6056, "mtime": 1750618918333, "results": "48", "hashOfConfig": "31"}, {"size": 4178, "mtime": 1750619313785, "results": "49", "hashOfConfig": "31"}, {"size": 1400, "mtime": 1750619325074, "results": "50", "hashOfConfig": "31"}, {"size": 6763, "mtime": 1750621680513, "results": "51", "hashOfConfig": "31"}, {"size": 1667, "mtime": 1750618896405, "results": "52", "hashOfConfig": "31"}, {"size": 5652, "mtime": 1750621654353, "results": "53", "hashOfConfig": "31"}, {"size": 1631, "mtime": 1750618027571, "results": "54", "hashOfConfig": "31"}, {"size": 2123, "mtime": 1750618027509, "results": "55", "hashOfConfig": "31"}, {"size": 1989, "mtime": 1750618027553, "results": "56", "hashOfConfig": "31"}, {"size": 1669, "mtime": 1750618860869, "results": "57", "hashOfConfig": "31"}, {"size": 166, "mtime": 1750617982530, "results": "58", "hashOfConfig": "31"}, {"size": 997, "mtime": 1750618230463, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ytk0ae", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\expertise\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\contact-info.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\expertise-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\expertise-tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx", [], ["147"], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mission-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\team-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\timeline-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts", [], [], {"ruleId": "148", "severity": 2, "message": "149", "line": 13, "column": 23, "nodeType": "150", "messageId": "151", "endLine": 13, "endColumn": 26, "suggestions": "152", "suppressions": "153"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["154", "155"], ["156"], {"messageId": "157", "fix": "158", "desc": "159"}, {"messageId": "160", "fix": "161", "desc": "162"}, {"kind": "163", "justification": "164"}, "suggestUnknown", {"range": "165", "text": "166"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "167", "text": "168"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", [367, 370], "unknown", [367, 370], "never"]