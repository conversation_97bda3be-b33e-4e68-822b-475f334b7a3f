{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Types for our database tables\nexport interface Project {\n  slug: string\n  title: string\n  summary: string | null\n  body_md: string | null\n  status: string\n  hero_url: string | null\n  tags: string[]\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContactMessage {\n  id: string\n  name: string\n  email: string\n  message: string\n  nda_agreed: boolean\n  company: string | null\n  project_type: string | null\n  budget: string | null\n  timeline: string | null\n  created_at: string\n}\n\n// API functions\nexport async function getProjects(): Promise<Project[]> {\n  const { data, error } = await supabase\n    .from('projects')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching projects:', error)\n    return []\n  }\n\n  return data || []\n}\n\nexport async function getProject(slug: string): Promise<Project | null> {\n  const { data, error } = await supabase\n    .from('projects')\n    .select('*')\n    .eq('slug', slug)\n    .single()\n\n  if (error) {\n    console.error('Error fetching project:', error)\n    return null\n  }\n\n  return data\n}\n\nexport async function submitContactMessage(message: Omit<ContactMessage, 'id' | 'created_at'>): Promise<boolean> {\n  const { error } = await supabase\n    .from('contact_messages')\n    .insert([message])\n\n  if (error) {\n    console.error('Error submitting contact message:', error)\n    return false\n  }\n\n  return true\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6B3C,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe,WAAW,IAAY;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAe,qBAAqB,OAAkD;IAC3F,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAC;KAAQ;IAEnB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/projects-grid.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { ArrowRight, Calendar, Tag } from 'lucide-react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { getProjects } from '@/lib/supabase'\n\nexport async function ProjectsGrid() {\n  const projects = await getProjects()\n\n  if (projects.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <p className=\"text-text-secondary\">No projects found.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\">\n      {projects.map((project, index) => (\n        <Card \n          key={project.slug} \n          className={`group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10 ${\n            index % 3 === 0 ? 'md:col-span-2 lg:col-span-1' : ''\n          }`}\n        >\n          <CardHeader className=\"pb-4\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex-1\">\n                <CardTitle className=\"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors\">\n                  {project.title}\n                </CardTitle>\n                <CardDescription className=\"mt-2 text-text-secondary line-clamp-2\">\n                  {project.summary || 'Innovative AI-powered solution designed to transform business operations.'}\n                </CardDescription>\n              </div>\n            </div>\n            \n            {/* Status Badge */}\n            <div className=\"flex items-center gap-2 mt-4\">\n              <Badge \n                variant=\"secondary\" \n                className={`text-xs px-2 py-1 ${\n                  project.status.toLowerCase().includes('complete') || project.status.toLowerCase().includes('beta')\n                    ? 'bg-accent/15 text-accent border-accent/20'\n                    : project.status.toLowerCase().includes('phase')\n                    ? 'bg-secondary/15 text-secondary border-secondary/20'\n                    : 'bg-tertiary/15 text-tertiary border-tertiary/20'\n                }`}\n              >\n                {project.status}\n              </Badge>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"pt-0\">\n            {/* Tags */}\n            {project.tags && project.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-4\">\n                {project.tags.slice(0, 3).map((tag) => (\n                  <span\n                    key={tag}\n                    className=\"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary\"\n                  >\n                    <Tag className=\"mr-1 h-3 w-3\" />\n                    {tag}\n                  </span>\n                ))}\n                {project.tags.length > 3 && (\n                  <span className=\"inline-flex items-center rounded-full bg-text-secondary/10 px-2 py-1 text-xs font-medium text-text-secondary\">\n                    +{project.tags.length - 3} more\n                  </span>\n                )}\n              </div>\n            )}\n\n            {/* Date */}\n            <div className=\"flex items-center text-xs text-text-secondary mb-4\">\n              <Calendar className=\"mr-1 h-3 w-3\" />\n              {new Date(project.created_at).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </div>\n\n            {/* CTA */}\n            <Button \n              asChild \n              variant=\"ghost\" \n              className=\"w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn\"\n            >\n              <Link href={`/projects/${project.slug}`}>\n                <span>Learn More</span>\n                <ArrowRight className=\"h-4 w-4 transition-transform group-hover/btn:translate-x-1\" />\n              </Link>\n            </Button>\n          </CardContent>\n        </Card>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAEjC,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;;;;;;IAGzC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;gBAEH,WAAW,CAAC,yIAAyI,EACnJ,QAAQ,MAAM,IAAI,gCAAgC,IAClD;;kCAEF,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,QAAQ,KAAK;;;;;;sDAEhB,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,QAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAW,CAAC,kBAAkB,EAC5B,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,UACvF,8CACA,QAAQ,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WACtC,uDACA,mDACJ;8CAED,QAAQ,MAAM;;;;;;;;;;;;;;;;;kCAKrB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAEpB,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,8OAAC;gCAAI,WAAU;;oCACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;oCAOR,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,8OAAC;wCAAK,WAAU;;4CAA+G;4CAC3H,QAAQ,IAAI,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;0CAOlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC,SAAS;wCACxD,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP;;;;;;;0CAIF,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,WAAU;0CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;;sDACrC,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;eAzEvB,QAAQ,IAAI;;;;;;;;;;AAiF3B", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/projects-loading.tsx"], "sourcesContent": ["import { Card, CardContent, CardHeader } from '@/components/ui/card'\n\nexport function ProjectsLoading() {\n  return (\n    <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\">\n      {Array.from({ length: 6 }).map((_, index) => (\n        <Card key={index} className=\"bg-surface-alt border-secondary/20 animate-pulse\">\n          <CardHeader className=\"pb-4\">\n            <div className=\"space-y-3\">\n              <div className=\"h-6 bg-text-secondary/20 rounded w-3/4\"></div>\n              <div className=\"h-4 bg-text-secondary/20 rounded w-full\"></div>\n              <div className=\"h-4 bg-text-secondary/20 rounded w-2/3\"></div>\n              <div className=\"h-6 bg-text-secondary/20 rounded w-20\"></div>\n            </div>\n          </CardHeader>\n          <CardContent className=\"pt-0\">\n            <div className=\"space-y-3\">\n              <div className=\"flex gap-2\">\n                <div className=\"h-5 bg-text-secondary/20 rounded-full w-16\"></div>\n                <div className=\"h-5 bg-text-secondary/20 rounded-full w-20\"></div>\n                <div className=\"h-5 bg-text-secondary/20 rounded-full w-14\"></div>\n              </div>\n              <div className=\"h-4 bg-text-secondary/20 rounded w-32\"></div>\n              <div className=\"h-10 bg-text-secondary/20 rounded w-full\"></div>\n            </div>\n          </CardContent>\n        </Card>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,gIAAA,CAAA,OAAI;gBAAa,WAAU;;kCAC1B,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAGnB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;eAjBV;;;;;;;;;;AAwBnB", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/app/projects/page.tsx"], "sourcesContent": ["import { Suspense } from 'react'\nimport { ProjectsGrid } from '@/components/projects-grid'\nimport { ProjectsLoading } from '@/components/projects-loading'\n\nexport const metadata = {\n  title: 'Projects | Metamorphic Labs',\n  description: 'Explore our portfolio of AI-powered solutions, intelligent automation systems, and custom software projects.',\n}\n\nexport default function ProjectsPage() {\n  return (\n    <div className=\"min-h-screen bg-surface\">\n      {/* Header Section */}\n      <section className=\"relative py-24 sm:py-32\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-4xl text-center\">\n            <h1 className=\"text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl\">\n              Our <span className=\"bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent\">Projects</span>\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-text-secondary sm:text-xl max-w-3xl mx-auto\">\n              Discover how we transform ideas into intelligent solutions. From AI platforms to automation systems, \n              each project showcases our commitment to innovation and excellence.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"pb-24 sm:pb-32\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <Suspense fallback={<ProjectsLoading />}>\n            <ProjectsGrid />\n          </Suspense>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAuE;kDAC/E,8OAAC;wCAAK,WAAU;kDAAuF;;;;;;;;;;;;0CAE7G,8OAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;;;;;;;;;;;0BAS7F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,wBAAU,8OAAC,yIAAA,CAAA,kBAAe;;;;;kCAClC,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}]}