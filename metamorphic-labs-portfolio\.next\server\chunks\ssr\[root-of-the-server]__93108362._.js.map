{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/contact-hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { MessageSquare, Clock, Shield } from 'lucide-react'\n\nexport function ContactHero() {\n  return (\n    <section className=\"relative py-24 sm:py-32 overflow-hidden\">\n      <div className=\"absolute inset-0 bg-gradient-to-br from-accent/10 via-secondary/5 to-tertiary/10\" />\n      \n      <div className=\"relative mx-auto max-w-7xl px-6 lg:px-8\">\n        <div className=\"mx-auto max-w-4xl text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h1 className=\"text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl lg:text-7xl\">\n              Let&apos;s{' '}\n              <span className=\"bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent\">\n                Build Together\n              </span>\n            </h1>\n          </motion.div>\n          \n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            className=\"mt-6 text-lg leading-8 text-text-secondary sm:text-xl lg:text-2xl max-w-3xl mx-auto\"\n          >\n            Ready to transform your ideas into intelligent solutions? We&apos;re here to help you \n            navigate the world of AI and custom software development.\n          </motion.p>\n          \n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            className=\"mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3\"\n          >\n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-secondary/10 mb-4\">\n                <MessageSquare className=\"h-8 w-8 text-secondary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">Free Consultation</h3>\n              <p className=\"text-text-secondary text-sm\">\n                Get expert advice on your project with no commitment required.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-tertiary/10 mb-4\">\n                <Clock className=\"h-8 w-8 text-tertiary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">Quick Response</h3>\n              <p className=\"text-text-secondary text-sm\">\n                We respond to all inquiries within 24 hours, usually much sooner.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col items-center text-center\">\n              <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-accent/10 mb-4\">\n                <Shield className=\"h-8 w-8 text-accent\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">NDA Protection</h3>\n              <p className=\"text-text-secondary text-sm\">\n                Your ideas are safe with us. We offer NDA protection for all discussions.\n              </p>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC;gCAAG,WAAU;;oCAAmF;oCACpF;kDACX,8OAAC;wCAAK,WAAU;kDAAuF;;;;;;;;;;;;;;;;;sCAM3G,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Types for our database tables\nexport interface Project {\n  slug: string\n  title: string\n  summary: string | null\n  body_md: string | null\n  status: string\n  hero_url: string | null\n  tags: string[]\n  created_at: string\n  updated_at: string\n}\n\nexport interface ContactMessage {\n  id: string\n  name: string\n  email: string\n  message: string\n  nda_agreed: boolean\n  company: string | null\n  project_type: string | null\n  budget: string | null\n  timeline: string | null\n  created_at: string\n}\n\n// API functions\nexport async function getProjects(): Promise<Project[]> {\n  const { data, error } = await supabase\n    .from('projects')\n    .select('*')\n    .order('created_at', { ascending: false })\n\n  if (error) {\n    console.error('Error fetching projects:', error)\n    return []\n  }\n\n  return data || []\n}\n\nexport async function getProject(slug: string): Promise<Project | null> {\n  const { data, error } = await supabase\n    .from('projects')\n    .select('*')\n    .eq('slug', slug)\n    .single()\n\n  if (error) {\n    console.error('Error fetching project:', error)\n    return null\n  }\n\n  return data\n}\n\nexport async function submitContactMessage(message: Omit<ContactMessage, 'id' | 'created_at'>): Promise<boolean> {\n  const { error } = await supabase\n    .from('contact_messages')\n    .insert([message])\n\n  if (error) {\n    console.error('Error submitting contact message:', error)\n    return false\n  }\n\n  return true\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AA6B3C,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,EAAE;IACX;IAEA,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe,WAAW,IAAY;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAe,qBAAqB,OAAkD;IAC3F,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAC;KAAQ;IAEnB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/contact-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ArrowRight, ArrowLeft, CheckCircle, Send, Loader2 } from 'lucide-react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\n\nimport { submitContactMessage } from '@/lib/supabase'\n\ninterface FormData {\n  name: string\n  email: string\n  company: string\n  projectType: string\n  budget: string\n  timeline: string\n  message: string\n  ndaAgreed: boolean\n}\n\nconst initialFormData: FormData = {\n  name: '',\n  email: '',\n  company: '',\n  projectType: '',\n  budget: '',\n  timeline: '',\n  message: '',\n  ndaAgreed: false\n}\n\nconst steps = [\n  { id: 1, title: 'Basic Info', description: 'Tell us about yourself' },\n  { id: 2, title: 'Project Details', description: 'Describe your project' },\n  { id: 3, title: 'Requirements', description: 'Budget and timeline' },\n  { id: 4, title: 'Message', description: 'Additional details' }\n]\n\nexport function ContactForm() {\n  const [currentStep, setCurrentStep] = useState(1)\n  const [formData, setFormData] = useState<FormData>(initialFormData)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n  const [errors, setErrors] = useState<Partial<FormData>>({})\n\n  const updateFormData = (field: keyof FormData, value: string | boolean) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }))\n    }\n  }\n\n  const validateStep = (step: number): boolean => {\n    const newErrors: Partial<FormData> = {}\n\n    switch (step) {\n      case 1:\n        if (!formData.name.trim()) newErrors.name = 'Name is required'\n        if (!formData.email.trim()) newErrors.email = 'Email is required'\n        else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) newErrors.email = 'Invalid email format'\n        break\n      case 2:\n        if (!formData.projectType) newErrors.projectType = 'Project type is required'\n        break\n      case 3:\n        if (!formData.budget) newErrors.budget = 'Budget range is required'\n        if (!formData.timeline) newErrors.timeline = 'Timeline is required'\n        break\n      case 4:\n        if (!formData.message.trim()) newErrors.message = 'Message is required'\n        break\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const nextStep = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length))\n    }\n  }\n\n  const prevStep = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1))\n  }\n\n  const handleSubmit = async () => {\n    if (!validateStep(4)) return\n\n    setIsSubmitting(true)\n    try {\n      const success = await submitContactMessage({\n        name: formData.name,\n        email: formData.email,\n        company: formData.company || null,\n        project_type: formData.projectType || null,\n        budget: formData.budget || null,\n        timeline: formData.timeline || null,\n        message: formData.message,\n        nda_agreed: formData.ndaAgreed\n      })\n\n      if (success) {\n        setIsSubmitted(true)\n      } else {\n        alert('Failed to submit form. Please try again.')\n      }\n    } catch (error) {\n      console.error('Form submission error:', error)\n      alert('An error occurred. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (isSubmitted) {\n    return (\n      <motion.div\n        initial={{ opacity: 0, scale: 0.95 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Card className=\"bg-surface-alt border-accent/20\">\n          <CardContent className=\"p-8 text-center\">\n            <div className=\"flex justify-center mb-6\">\n              <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-accent/10\">\n                <CheckCircle className=\"h-8 w-8 text-accent\" />\n              </div>\n            </div>\n            <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n              Thank You!\n            </h3>\n            <p className=\"text-text-secondary mb-6\">\n              We&apos;ve received your message and will get back to you within 24 hours. \n              We&apos;re excited to discuss your project!\n            </p>\n            <Button\n              onClick={() => {\n                setIsSubmitted(false)\n                setCurrentStep(1)\n                setFormData(initialFormData)\n              }}\n              variant=\"outline\"\n            >\n              Send Another Message\n            </Button>\n          </CardContent>\n        </Card>\n      </motion.div>\n    )\n  }\n\n  return (\n    <Card className=\"bg-surface-alt border-secondary/20\">\n      <CardHeader>\n        <CardTitle className=\"text-2xl font-bold text-text-primary\">\n          Start Your Project\n        </CardTitle>\n        \n        {/* Progress Steps */}\n        <div className=\"flex items-center justify-between mt-6\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-center\">\n              <div className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${\n                currentStep >= step.id\n                  ? 'bg-secondary text-white'\n                  : 'bg-surface text-text-secondary'\n              }`}>\n                {currentStep > step.id ? (\n                  <CheckCircle className=\"h-4 w-4\" />\n                ) : (\n                  step.id\n                )}\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`h-0.5 w-8 mx-2 ${\n                  currentStep > step.id ? 'bg-secondary' : 'bg-surface'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n        \n        <div className=\"mt-4\">\n          <h3 className=\"font-semibold text-text-primary\">{steps[currentStep - 1].title}</h3>\n          <p className=\"text-sm text-text-secondary\">{steps[currentStep - 1].description}</p>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"p-6\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentStep}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"space-y-6\"\n          >\n            {/* Step 1: Basic Info */}\n            {currentStep === 1 && (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Full Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.name}\n                    onChange={(e) => updateFormData('name', e.target.value)}\n                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 ${\n                      errors.name ? 'border-red-500' : 'border-secondary/20'\n                    }`}\n                    placeholder=\"Enter your full name\"\n                  />\n                  {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => updateFormData('email', e.target.value)}\n                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 ${\n                      errors.email ? 'border-red-500' : 'border-secondary/20'\n                    }`}\n                    placeholder=\"Enter your email address\"\n                  />\n                  {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Company (Optional)\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.company}\n                    onChange={(e) => updateFormData('company', e.target.value)}\n                    className=\"w-full px-4 py-3 rounded-lg border border-secondary/20 bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50\"\n                    placeholder=\"Enter your company name\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Step 2: Project Details */}\n            {currentStep === 2 && (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Project Type *\n                  </label>\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    {['AI Platform Development', 'Intelligent Automation', 'Custom Software', 'Consulting & Strategy', 'Other'].map((type) => (\n                      <button\n                        key={type}\n                        onClick={() => updateFormData('projectType', type)}\n                        className={`p-3 rounded-lg border text-left transition-all ${\n                          formData.projectType === type\n                            ? 'border-secondary bg-secondary/10 text-secondary'\n                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'\n                        }`}\n                      >\n                        {type}\n                      </button>\n                    ))}\n                  </div>\n                  {errors.projectType && <p className=\"text-red-500 text-sm mt-1\">{errors.projectType}</p>}\n                </div>\n              </div>\n            )}\n\n            {/* Step 3: Requirements */}\n            {currentStep === 3 && (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Budget Range *\n                  </label>\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    {['$10K - $25K', '$25K - $50K', '$50K - $100K', '$100K - $250K', '$250K+'].map((budget) => (\n                      <button\n                        key={budget}\n                        onClick={() => updateFormData('budget', budget)}\n                        className={`p-3 rounded-lg border text-left transition-all ${\n                          formData.budget === budget\n                            ? 'border-tertiary bg-tertiary/10 text-tertiary'\n                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'\n                        }`}\n                      >\n                        {budget}\n                      </button>\n                    ))}\n                  </div>\n                  {errors.budget && <p className=\"text-red-500 text-sm mt-1\">{errors.budget}</p>}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Timeline *\n                  </label>\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    {['ASAP', '1-3 months', '3-6 months', '6-12 months', '12+ months'].map((timeline) => (\n                      <button\n                        key={timeline}\n                        onClick={() => updateFormData('timeline', timeline)}\n                        className={`p-3 rounded-lg border text-left transition-all ${\n                          formData.timeline === timeline\n                            ? 'border-accent bg-accent/10 text-accent'\n                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'\n                        }`}\n                      >\n                        {timeline}\n                      </button>\n                    ))}\n                  </div>\n                  {errors.timeline && <p className=\"text-red-500 text-sm mt-1\">{errors.timeline}</p>}\n                </div>\n              </div>\n            )}\n\n            {/* Step 4: Message */}\n            {currentStep === 4 && (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-text-primary mb-2\">\n                    Project Description *\n                  </label>\n                  <textarea\n                    value={formData.message}\n                    onChange={(e) => updateFormData('message', e.target.value)}\n                    rows={6}\n                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 resize-none ${\n                      errors.message ? 'border-red-500' : 'border-secondary/20'\n                    }`}\n                    placeholder=\"Tell us about your project, goals, and any specific requirements...\"\n                  />\n                  {errors.message && <p className=\"text-red-500 text-sm mt-1\">{errors.message}</p>}\n                </div>\n\n                <div className=\"flex items-start space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"nda\"\n                    checked={formData.ndaAgreed}\n                    onChange={(e) => updateFormData('ndaAgreed', e.target.checked)}\n                    className=\"mt-1 h-4 w-4 text-secondary focus:ring-secondary border-secondary/20 rounded\"\n                  />\n                  <label htmlFor=\"nda\" className=\"text-sm text-text-secondary\">\n                    I would like to sign an NDA before discussing project details\n                  </label>\n                </div>\n              </div>\n            )}\n          </motion.div>\n        </AnimatePresence>\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            onClick={prevStep}\n            variant=\"outline\"\n            disabled={currentStep === 1}\n            className=\"flex items-center gap-2\"\n          >\n            <ArrowLeft className=\"h-4 w-4\" />\n            Previous\n          </Button>\n\n          {currentStep < steps.length ? (\n            <Button\n              onClick={nextStep}\n              className=\"flex items-center gap-2 bg-secondary text-white hover:bg-secondary/90\"\n            >\n              Next\n              <ArrowRight className=\"h-4 w-4\" />\n            </Button>\n          ) : (\n            <Button\n              onClick={handleSubmit}\n              disabled={isSubmitting}\n              className=\"flex items-center gap-2 bg-accent text-surface hover:bg-accent/90\"\n            >\n              {isSubmitting ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  Sending...\n                </>\n              ) : (\n                <>\n                  <Send className=\"h-4 w-4\" />\n                  Send Message\n                </>\n              )}\n            </Button>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AARA;;;;;;;;AAqBA,MAAM,kBAA4B;IAChC,MAAM;IACN,OAAO;IACP,SAAS;IACT,aAAa;IACb,QAAQ;IACR,UAAU;IACV,SAAS;IACT,WAAW;AACb;AAEA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,OAAO;QAAc,aAAa;IAAyB;IACpE;QAAE,IAAI;QAAG,OAAO;QAAmB,aAAa;IAAwB;IACxE;QAAE,IAAI;QAAG,OAAO;QAAgB,aAAa;IAAsB;IACnE;QAAE,IAAI;QAAG,OAAO;QAAW,aAAa;IAAqB;CAC9D;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAEzD,MAAM,iBAAiB,CAAC,OAAuB;QAC7C,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACpD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,YAA+B,CAAC;QAEtC,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;gBAC5C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;qBACzC,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG,UAAU,KAAK,GAAG;gBACjE;YACF,KAAK;gBACH,IAAI,CAAC,SAAS,WAAW,EAAE,UAAU,WAAW,GAAG;gBACnD;YACF,KAAK;gBACH,IAAI,CAAC,SAAS,MAAM,EAAE,UAAU,MAAM,GAAG;gBACzC,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;gBAC7C;YACF,KAAK;gBACH,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG;gBAClD;QACJ;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,WAAW;QACf,IAAI,aAAa,cAAc;YAC7B,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG,MAAM,MAAM;QACxD;IACF;IAEA,MAAM,WAAW;QACf,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;IAC5C;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,IAAI;QAEtB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzC,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,SAAS,SAAS,OAAO,IAAI;gBAC7B,cAAc,SAAS,WAAW,IAAI;gBACtC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,UAAU,SAAS,QAAQ,IAAI;gBAC/B,SAAS,SAAS,OAAO;gBACzB,YAAY,SAAS,SAAS;YAChC;YAEA,IAAI,SAAS;gBACX,eAAe;YACjB,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAK;YACnC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU;YAAI;sBAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG3B,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAIxC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;gCACP,eAAe;gCACf,eAAe;gCACf,YAAY;4BACd;4BACA,SAAQ;sCACT;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAuC;;;;;;kCAK5D,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAI,WAAW,CAAC,0EAA0E,EACzF,eAAe,KAAK,EAAE,GAClB,4BACA,kCACJ;kDACC,cAAc,KAAK,EAAE,iBACpB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;mDAEvB,KAAK,EAAE;;;;;;oCAGV,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;wCAAI,WAAW,CAAC,eAAe,EAC9B,cAAc,KAAK,EAAE,GAAG,iBAAiB,cACzC;;;;;;;+BAfI,KAAK,EAAE;;;;;;;;;;kCAqBrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC,KAAK,CAAC,cAAc,EAAE,CAAC,KAAK;;;;;;0CAC7E,8OAAC;gCAAE,WAAU;0CAA+B,KAAK,CAAC,cAAc,EAAE,CAAC,WAAW;;;;;;;;;;;;;;;;;;0BAIlF,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCAGT,gBAAgB,mBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACtD,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,IAAI,GAAG,mBAAmB,uBACjC;oDACF,aAAY;;;;;;gDAEb,OAAO,IAAI,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;;;;;;;sDAGvE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;oDACvD,WAAW,CAAC,mJAAmJ,EAC7J,OAAO,KAAK,GAAG,mBAAmB,uBAClC;oDACF,aAAY;;;;;;gDAEb,OAAO,KAAK,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK;;;;;;;;;;;;sDAGzE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDACC,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;gCAOnB,gBAAgB,mBACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAmD;;;;;;0DAGpE,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAA2B;oDAA0B;oDAAmB;oDAAyB;iDAAQ,CAAC,GAAG,CAAC,CAAC,qBAC/G,8OAAC;wDAEC,SAAS,IAAM,eAAe,eAAe;wDAC7C,WAAW,CAAC,+CAA+C,EACzD,SAAS,WAAW,KAAK,OACrB,oDACA,8EACJ;kEAED;uDARI;;;;;;;;;;4CAYV,OAAO,WAAW,kBAAI,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,WAAW;;;;;;;;;;;;;;;;;gCAMxF,gBAAgB,mBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAe;wDAAe;wDAAgB;wDAAiB;qDAAS,CAAC,GAAG,CAAC,CAAC,uBAC9E,8OAAC;4DAEC,SAAS,IAAM,eAAe,UAAU;4DACxC,WAAW,CAAC,+CAA+C,EACzD,SAAS,MAAM,KAAK,SAChB,iDACA,8EACJ;sEAED;2DARI;;;;;;;;;;gDAYV,OAAO,MAAM,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,MAAM;;;;;;;;;;;;sDAG3E,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAQ;wDAAc;wDAAc;wDAAe;qDAAa,CAAC,GAAG,CAAC,CAAC,yBACtE,8OAAC;4DAEC,SAAS,IAAM,eAAe,YAAY;4DAC1C,WAAW,CAAC,+CAA+C,EACzD,SAAS,QAAQ,KAAK,WAClB,2CACA,8EACJ;sEAED;2DARI;;;;;;;;;;gDAYV,OAAO,QAAQ,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;gCAMlF,gBAAgB,mBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,8OAAC;oDACC,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;oDACzD,MAAM;oDACN,WAAW,CAAC,+JAA+J,EACzK,OAAO,OAAO,GAAG,mBAAmB,uBACpC;oDACF,aAAY;;;;;;gDAEb,OAAO,OAAO,kBAAI,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO;;;;;;;;;;;;sDAG7E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,SAAS,SAAS,SAAS;oDAC3B,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,OAAO;oDAC7D,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAM,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;2BAhK9D;;;;;;;;;;kCA0KT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAIlC,cAAc,MAAM,MAAM,iBACzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;qDAGxB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,6BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAyB;;iEAI9C;;sDACE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/contact-info.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Mail, MapPin, Clock, Calendar, Phone, MessageSquare } from 'lucide-react'\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\n\nconst contactMethods = [\n  {\n    icon: Mail,\n    title: 'Email Us',\n    description: 'Send us an email and we\\'ll respond within 24 hours',\n    value: '<EMAIL>',\n    action: 'mailto:<EMAIL>',\n    color: 'secondary'\n  },\n  {\n    icon: Phone,\n    title: 'Schedule a Call',\n    description: 'Book a free 30-minute consultation call',\n    value: 'Book Meeting',\n    action: 'https://calendly.com/metamorphiclabs/consultation',\n    color: 'tertiary'\n  },\n  {\n    icon: MessageSquare,\n    title: 'Live Chat',\n    description: 'Chat with our team during business hours',\n    value: 'Start Chat',\n    action: '#',\n    color: 'accent'\n  }\n]\n\nconst officeInfo = [\n  {\n    icon: MapPin,\n    title: 'Headquarters',\n    value: 'San Francisco, CA\\nUnited States'\n  },\n  {\n    icon: Clock,\n    title: 'Business Hours',\n    value: 'Monday - Friday\\n9:00 AM - 6:00 PM PST'\n  },\n  {\n    icon: Calendar,\n    title: 'Response Time',\n    value: 'Within 24 hours\\nUsually much sooner'\n  }\n]\n\nconst faqs = [\n  {\n    question: 'How long does a typical project take?',\n    answer: 'Project timelines vary based on complexity, but most projects range from 3-12 months. We provide detailed timelines during our initial consultation.'\n  },\n  {\n    question: 'Do you work with startups?',\n    answer: 'Absolutely! We work with companies of all sizes, from early-stage startups to Fortune 500 enterprises.'\n  },\n  {\n    question: 'What technologies do you specialize in?',\n    answer: 'We specialize in AI/ML technologies, modern web frameworks (React, Next.js), cloud platforms (AWS, GCP, Azure), and enterprise software development.'\n  },\n  {\n    question: 'Do you provide ongoing support?',\n    answer: 'Yes, we offer comprehensive support and maintenance packages to ensure your solutions continue to perform optimally.'\n  }\n]\n\nexport function ContactInfo() {\n  return (\n    <div className=\"space-y-8\">\n      {/* Contact Methods */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <h2 className=\"text-2xl font-bold text-text-primary mb-6\">\n          Other Ways to Reach Us\n        </h2>\n        <div className=\"space-y-4\">\n          {contactMethods.map((method, index) => (\n            <motion.div\n              key={method.title}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className={`flex h-12 w-12 items-center justify-center rounded-full bg-${method.color}/10 flex-shrink-0`}>\n                      <method.icon className={`h-6 w-6 text-${method.color}`} />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-text-primary mb-1\">\n                        {method.title}\n                      </h3>\n                      <p className=\"text-text-secondary text-sm mb-3\">\n                        {method.description}\n                      </p>\n                      <Button\n                        asChild\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className={`border-${method.color}/20 text-${method.color} hover:bg-${method.color}/10`}\n                      >\n                        <a\n                          href={method.action}\n                          target={method.action.startsWith('http') ? '_blank' : undefined}\n                          rel={method.action.startsWith('http') ? 'noopener noreferrer' : undefined}\n                        >\n                          {method.value}\n                        </a>\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Office Information */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.3 }}\n      >\n        <Card className=\"bg-surface-alt border-secondary/20\">\n          <CardHeader>\n            <CardTitle className=\"text-xl font-bold text-text-primary\">\n              Office Information\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"p-6 pt-0\">\n            <div className=\"space-y-6\">\n              {officeInfo.map((info) => (\n                <div key={info.title} className=\"flex items-start space-x-4\">\n                  <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-secondary/10 flex-shrink-0\">\n                    <info.icon className=\"h-5 w-5 text-secondary\" />\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-text-primary mb-1\">\n                      {info.title}\n                    </h4>\n                    <p className=\"text-text-secondary text-sm whitespace-pre-line\">\n                      {info.value}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* FAQ Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.6 }}\n      >\n        <Card className=\"bg-surface-alt border-secondary/20\">\n          <CardHeader>\n            <CardTitle className=\"text-xl font-bold text-text-primary\">\n              Frequently Asked Questions\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"p-6 pt-0\">\n            <div className=\"space-y-6\">\n              {faqs.map((faq, faqIndex) => (\n                <div key={faqIndex}>\n                  <h4 className=\"font-medium text-text-primary mb-2\">\n                    {faq.question}\n                  </h4>\n                  <p className=\"text-text-secondary text-sm leading-relaxed\">\n                    {faq.answer}\n                  </p>\n                  {faqIndex < faqs.length - 1 && (\n                    <hr className=\"mt-6 border-secondary/20\" />\n                  )}\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Emergency Contact */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6, delay: 0.9 }}\n      >\n        <Card className=\"bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20\">\n          <CardContent className=\"p-6 text-center\">\n            <h3 className=\"text-lg font-semibold text-text-primary mb-2\">\n              Need Urgent Support?\n            </h3>\n            <p className=\"text-text-secondary text-sm mb-4\">\n              For existing clients with critical issues, we offer 24/7 emergency support.\n            </p>\n            <Button\n              asChild\n              className=\"bg-accent text-surface hover:bg-accent/90\"\n            >\n              <a href=\"tel:******-METAMORPHIC\">\n                Call Emergency Line\n              </a>\n            </Button>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOA,MAAM,iBAAiB;IACrB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;QACP,QAAQ;QACR,OAAO;IACT;CACD;AAED,MAAM,aAAa;IACjB;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAG1D,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,2DAA2D,EAAE,OAAO,KAAK,CAAC,iBAAiB,CAAC;8DAC3G,cAAA,8OAAC,OAAO,IAAI;wDAAC,WAAW,CAAC,aAAa,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;8DAExD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,8OAAC;4DAAE,WAAU;sEACV,OAAO,WAAW;;;;;;sEAErB,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,WAAW,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,SAAS,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC;sEAEvF,cAAA,8OAAC;gEACC,MAAM,OAAO,MAAM;gEACnB,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC,UAAU,WAAW;gEACtD,KAAK,OAAO,MAAM,CAAC,UAAU,CAAC,UAAU,wBAAwB;0EAE/D,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7BpB,OAAO,KAAK;;;;;;;;;;;;;;;;0BA0CzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsC;;;;;;;;;;;sCAI7D,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;;;;;;;;uCATP,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoB9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAsC;;;;;;;;;;;sCAI7D,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,yBACd,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;4CAEZ,WAAW,KAAK,MAAM,GAAG,mBACxB,8OAAC;gDAAG,WAAU;;;;;;;uCARR;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkBpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAGhD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCAAE,MAAK;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}]}