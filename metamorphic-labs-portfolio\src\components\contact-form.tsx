'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowRight, ArrowLeft, CheckCircle, Send, Loader2 } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { submitContactMessage } from '@/lib/supabase'

interface FormData {
  name: string
  email: string
  company: string
  projectType: string
  budget: string
  timeline: string
  message: string
  ndaAgreed: boolean
}

const initialFormData: FormData = {
  name: '',
  email: '',
  company: '',
  projectType: '',
  budget: '',
  timeline: '',
  message: '',
  ndaAgreed: false
}

const steps = [
  { id: 1, title: 'Basic Info', description: 'Tell us about yourself' },
  { id: 2, title: 'Project Details', description: 'Describe your project' },
  { id: 3, title: 'Requirements', description: 'Budget and timeline' },
  { id: 4, title: 'Message', description: 'Additional details' }
]

export function ContactForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<FormData>(initialFormData)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [errors, setErrors] = useState<Partial<FormData>>({})

  const updateFormData = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<FormData> = {}

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = 'Name is required'
        if (!formData.email.trim()) newErrors.email = 'Email is required'
        else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Invalid email format'
        break
      case 2:
        if (!formData.projectType) newErrors.projectType = 'Project type is required'
        break
      case 3:
        if (!formData.budget) newErrors.budget = 'Budget range is required'
        if (!formData.timeline) newErrors.timeline = 'Timeline is required'
        break
      case 4:
        if (!formData.message.trim()) newErrors.message = 'Message is required'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(4)) return

    setIsSubmitting(true)
    try {
      const success = await submitContactMessage({
        name: formData.name,
        email: formData.email,
        company: formData.company || null,
        project_type: formData.projectType || null,
        budget: formData.budget || null,
        timeline: formData.timeline || null,
        message: formData.message,
        nda_agreed: formData.ndaAgreed
      })

      if (success) {
        setIsSubmitted(true)
      } else {
        alert('Failed to submit form. Please try again.')
      }
    } catch (error) {
      console.error('Form submission error:', error)
      alert('An error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-surface-alt border-accent/20">
          <CardContent className="p-8 text-center">
            <div className="flex justify-center mb-6">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-accent/10">
                <CheckCircle className="h-8 w-8 text-accent" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-text-primary mb-4">
              Thank You!
            </h3>
            <p className="text-text-secondary mb-6">
              We&apos;ve received your message and will get back to you within 24 hours. 
              We&apos;re excited to discuss your project!
            </p>
            <Button
              onClick={() => {
                setIsSubmitted(false)
                setCurrentStep(1)
                setFormData(initialFormData)
              }}
              variant="outline"
            >
              Send Another Message
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <Card className="bg-surface-alt border-secondary/20">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-text-primary">
          Start Your Project
        </CardTitle>
        
        {/* Progress Steps */}
        <div className="flex items-center justify-between mt-6">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                currentStep >= step.id
                  ? 'bg-secondary text-white'
                  : 'bg-surface text-text-secondary'
              }`}>
                {currentStep > step.id ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  step.id
                )}
              </div>
              {index < steps.length - 1 && (
                <div className={`h-0.5 w-8 mx-2 ${
                  currentStep > step.id ? 'bg-secondary' : 'bg-surface'
                }`} />
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-4">
          <h3 className="font-semibold text-text-primary">{steps[currentStep - 1].title}</h3>
          <p className="text-sm text-text-secondary">{steps[currentStep - 1].description}</p>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Step 1: Basic Info */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => updateFormData('name', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 ${
                      errors.name ? 'border-red-500' : 'border-secondary/20'
                    }`}
                    placeholder="Enter your full name"
                  />
                  {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 ${
                      errors.email ? 'border-red-500' : 'border-secondary/20'
                    }`}
                    placeholder="Enter your email address"
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Company (Optional)
                  </label>
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => updateFormData('company', e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border border-secondary/20 bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50"
                    placeholder="Enter your company name"
                  />
                </div>
              </div>
            )}

            {/* Step 2: Project Details */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Project Type *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {['AI Platform Development', 'Intelligent Automation', 'Custom Software', 'Consulting & Strategy', 'Other'].map((type) => (
                      <button
                        key={type}
                        onClick={() => updateFormData('projectType', type)}
                        className={`p-3 rounded-lg border text-left transition-all ${
                          formData.projectType === type
                            ? 'border-secondary bg-secondary/10 text-secondary'
                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'
                        }`}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                  {errors.projectType && <p className="text-red-500 text-sm mt-1">{errors.projectType}</p>}
                </div>
              </div>
            )}

            {/* Step 3: Requirements */}
            {currentStep === 3 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Budget Range *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {['$10K - $25K', '$25K - $50K', '$50K - $100K', '$100K - $250K', '$250K+'].map((budget) => (
                      <button
                        key={budget}
                        onClick={() => updateFormData('budget', budget)}
                        className={`p-3 rounded-lg border text-left transition-all ${
                          formData.budget === budget
                            ? 'border-tertiary bg-tertiary/10 text-tertiary'
                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'
                        }`}
                      >
                        {budget}
                      </button>
                    ))}
                  </div>
                  {errors.budget && <p className="text-red-500 text-sm mt-1">{errors.budget}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Timeline *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {['ASAP', '1-3 months', '3-6 months', '6-12 months', '12+ months'].map((timeline) => (
                      <button
                        key={timeline}
                        onClick={() => updateFormData('timeline', timeline)}
                        className={`p-3 rounded-lg border text-left transition-all ${
                          formData.timeline === timeline
                            ? 'border-accent bg-accent/10 text-accent'
                            : 'border-secondary/20 bg-surface text-text-primary hover:border-secondary/40'
                        }`}
                      >
                        {timeline}
                      </button>
                    ))}
                  </div>
                  {errors.timeline && <p className="text-red-500 text-sm mt-1">{errors.timeline}</p>}
                </div>
              </div>
            )}

            {/* Step 4: Message */}
            {currentStep === 4 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Project Description *
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => updateFormData('message', e.target.value)}
                    rows={6}
                    className={`w-full px-4 py-3 rounded-lg border bg-surface text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-secondary/50 resize-none ${
                      errors.message ? 'border-red-500' : 'border-secondary/20'
                    }`}
                    placeholder="Tell us about your project, goals, and any specific requirements..."
                  />
                  {errors.message && <p className="text-red-500 text-sm mt-1">{errors.message}</p>}
                </div>

                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="nda"
                    checked={formData.ndaAgreed}
                    onChange={(e) => updateFormData('ndaAgreed', e.target.checked)}
                    className="mt-1 h-4 w-4 text-secondary focus:ring-secondary border-secondary/20 rounded"
                  />
                  <label htmlFor="nda" className="text-sm text-text-secondary">
                    I would like to sign an NDA before discussing project details
                  </label>
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            onClick={prevStep}
            variant="outline"
            disabled={currentStep === 1}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          {currentStep < steps.length ? (
            <Button
              onClick={nextStep}
              className="flex items-center gap-2 bg-secondary text-white hover:bg-secondary/90"
            >
              Next
              <ArrowRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex items-center gap-2 bg-accent text-surface hover:bg-accent/90"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  Send Message
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
