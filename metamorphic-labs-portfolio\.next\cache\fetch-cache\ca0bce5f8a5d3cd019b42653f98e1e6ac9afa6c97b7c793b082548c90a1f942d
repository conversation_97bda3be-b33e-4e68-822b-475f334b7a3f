{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e5426adb0acd5-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?select=%2A&slug=eq.living-pipeline", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:59:53 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=EV4rQfSW9Uwdt.****************************************-*******-0Z_5Uk83Amt.4M4q9EGJkEWfqrDwLdPMrOm31x.QUJIU0rv_KOn3IKxmxOzku1VGanWnevDuKG2nW8Z9BNrqpf.gY6C6KFOfxDwH718WQEg; path=/; expires=Sun, 22-Jun-25 20:29:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "eyJzbHVnIjoibGl2aW5nLXBpcGVsaW5lIiwidGl0bGUiOiJMaXZpbmcgUGlwZWxpbmUgKEFJLU9wdGltaXplZCBDSS9DRCkiLCJzdW1tYXJ5IjoiQSBcImxpdmluZ1wiIGV2ZW50LWRyaXZlbiBDSS9DRCBzdGFjayB0aGF0IHVzZXMgTUwgdG8gc3BvdCBmbGFraW5lc3MsIG9wdGltaXplIGJ1aWxkIGNhY2hlcywgYW5kIGNob29zZSByb2xsb3V0IHN0cmF0ZWdpZXMgKGJsdWUtZ3JlZW4sIGNhbmFyeSkgb24gdGhlIGZseS4iLCJib2R5X21kIjpudWxsLCJzdGF0dXMiOiJDb25jZXB0IiwiaGVyb191cmwiOiIvaW1hZ2VzL3Byb2plY3RzL2xpdmluZy1waXBlbGluZS5qcGciLCJ0YWdzIjpbIkNJL0NEIiwgIkFub21hbHkgRGV0ZWN0aW9uIiwgIlNlbGYtT3B0aW1pemluZyJdLCJjcmVhdGVkX2F0IjoiMjAyNS0wNi0yMVQyMzoxNDo0Ni43MjQyMjgrMDA6MDAiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNi0yMVQyMzoxNDo0Ni43MjQyMjgrMDA6MDAifQ==", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&slug=eq.living-pipeline"}, "revalidate": 31536000, "tags": []}