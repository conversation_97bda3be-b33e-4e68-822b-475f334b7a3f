(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/mdx-content.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MDXContent": (()=>MDXContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$markdown$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__Markdown__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/react-markdown/lib/index.js [app-client] (ecmascript) <export Markdown as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$syntax$2d$highlighter$2f$dist$2f$esm$2f$prism$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Prism$3e$__ = __turbopack_context__.i("[project]/node_modules/react-syntax-highlighter/dist/esm/prism.js [app-client] (ecmascript) <export default as Prism>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$syntax$2d$highlighter$2f$dist$2f$esm$2f$styles$2f$prism$2f$one$2d$dark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__oneDark$3e$__ = __turbopack_context__.i("[project]/node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js [app-client] (ecmascript) <export default as oneDark>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function MDXContent({ content }) {
    _s();
    const components = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MDXContent.useMemo[components]": ()=>({
                h1: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-text-primary mb-6 mt-8 first:mt-0",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 18,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                h2: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-2xl font-semibold text-text-primary mb-4 mt-8 first:mt-0",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 23,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                h3: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-semibold text-text-primary mb-3 mt-6",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 28,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                h4: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-lg font-semibold text-text-primary mb-2 mt-4",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 33,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                p: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-text-secondary leading-7 mb-4",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 38,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                ul: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "list-disc list-inside text-text-secondary mb-4 space-y-2",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 43,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                ol: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                            className: "list-decimal list-inside text-text-secondary mb-4 space-y-2",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 48,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                li: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "leading-7",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 53,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                strong: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            className: "font-semibold text-text-primary",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 58,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                em: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("em", {
                            className: "italic text-text-primary",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 63,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                code: ({
                    "MDXContent.useMemo[components]": ({ inline, className, children, ...props })=>{
                        const match = /language-(\w+)/.exec(className || '');
                        return !inline && match ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "my-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$syntax$2d$highlighter$2f$dist$2f$esm$2f$prism$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Prism$3e$__["Prism"], {
                                style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$syntax$2d$highlighter$2f$dist$2f$esm$2f$styles$2f$prism$2f$one$2d$dark$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__oneDark$3e$__["oneDark"],
                                language: match[1],
                                PreTag: "div",
                                className: "rounded-lg",
                                ...props,
                                children: String(children).replace(/\n$/, '')
                            }, void 0, false, {
                                fileName: "[project]/src/components/mdx-content.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 70,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                            className: "bg-tertiary/10 text-tertiary px-1.5 py-0.5 rounded text-sm font-mono",
                            ...props,
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 82,
                            columnNumber: 9
                        }, this);
                    }
                })["MDXContent.useMemo[components]"],
                blockquote: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("blockquote", {
                            className: "border-l-4 border-secondary pl-4 my-6 italic text-text-secondary",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 88,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                a: ({
                    "MDXContent.useMemo[components]": ({ href, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            href: href,
                            className: "text-secondary hover:text-secondary/80 underline underline-offset-2 transition-colors",
                            target: href?.startsWith('http') ? '_blank' : undefined,
                            rel: href?.startsWith('http') ? 'noopener noreferrer' : undefined,
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 93,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                hr: ({
                    "MDXContent.useMemo[components]": ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                            className: "border-secondary/20 my-8"
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 103,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                table: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-x-auto my-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                className: "min-w-full border border-secondary/20 rounded-lg",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/mdx-content.tsx",
                                lineNumber: 107,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 106,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                thead: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            className: "bg-secondary/5",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 113,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                th: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                            className: "px-4 py-2 text-left font-semibold text-text-primary border-b border-secondary/20",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 118,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"],
                td: ({
                    "MDXContent.useMemo[components]": ({ children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                            className: "px-4 py-2 text-text-secondary border-b border-secondary/10",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/mdx-content.tsx",
                            lineNumber: 123,
                            columnNumber: 7
                        }, this)
                })["MDXContent.useMemo[components]"]
            })
    }["MDXContent.useMemo[components]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "prose prose-lg max-w-none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$markdown$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__Markdown__as__default$3e$__["default"], {
            components: components,
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/mdx-content.tsx",
            lineNumber: 131,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/mdx-content.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
}
_s(MDXContent, "JWMW5pANxrmlEVuZJ9ic4q5EcKI=");
_c = MDXContent;
var _c;
__turbopack_context__.k.register(_c, "MDXContent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_mdx-content_tsx_ff8b55b4._.js.map