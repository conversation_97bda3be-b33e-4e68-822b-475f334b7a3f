{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e54259c734c9d-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?select=%2A&slug=eq.ai-integration-broker", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:59:53 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=U3tBUtisoVj1Z2QIl3ngk8pBBKexl1kAJS8HXjxZg6Q-1750622393-*******-xJIdsX1FDn2gKRvd._AQHnvvsb_mnOSpBJs9aflyXbHcmFyfG4fHcjInGlS1pRgWh5suHOo1dv32bVljXs24LCr8HyDEEedKk.ulDkXIlT4; path=/; expires=Sun, 22-Jun-25 20:29:53 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "eyJzbHVnIjoiYWktaW50ZWdyYXRpb24tYnJva2VyIiwidGl0bGUiOiJBSSBJbnRlZ3JhdGlvbiAmIEJyb2tlciBTeXN0ZW0iLCJzdW1tYXJ5IjoiQSBzbWFydCBkaXNwYXRjaGVyIHRoYXQgcm91dGVzIHVzZXIgcXVlcmllcyB0byB0aGUgb3B0aW1hbCBMTE0gKENoYXRHUFQsIENsYXVkZSwgUGVycGxleGl0eSwgR3JvaykgYW5kIHN0aXRjaGVzIG11bHRpLWFnZW50IGNvbGxhYm9yYXRpb24gY2hhaW5zLiBPQXV0aCAmIHNlY3VyZSB0b2tlbiB2YXVsdCBhcmUgY29tcGxldGU7IFNhYVMgd29ya2Zsb3cgcGx1bWJpbmcgYW5kIFByb21wdEJveCBleHBvcnQgYXJlIG5leHQuIiwiYm9keV9tZCI6bnVsbCwic3RhdHVzIjoiUGhhc2UgMiDigJMgQnJva2VyIENvbXBsZXRlIiwiaGVyb191cmwiOiIvaW1hZ2VzL3Byb2plY3RzL2FpLWJyb2tlci5qcGciLCJ0YWdzIjpbIk11bHRpLUxMTSIsICJPQXV0aCIsICJSb3V0aW5nIl0sImNyZWF0ZWRfYXQiOiIyMDI1LTA2LTIxVDIzOjE0OjQ2LjcyNDIyOCswMDowMCIsInVwZGF0ZWRfYXQiOiIyMDI1LTA2LTIxVDIzOjE0OjQ2LjcyNDIyOCswMDowMCJ9", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&slug=eq.ai-integration-broker"}, "revalidate": 31536000, "tags": []}