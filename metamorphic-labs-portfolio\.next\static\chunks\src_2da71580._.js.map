{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\n\ntype Theme = 'dark' | 'light'\n\ntype ThemeProviderProps = {\n  children: React.ReactNode\n  defaultTheme?: Theme\n  storageKey?: string\n}\n\ntype ThemeProviderState = {\n  theme: Theme\n  setTheme: (theme: Theme) => void\n  toggleTheme: () => void\n}\n\nconst initialState: ThemeProviderState = {\n  theme: 'dark',\n  setTheme: () => null,\n  toggleTheme: () => null,\n}\n\nconst ThemeProviderContext = createContext<ThemeProviderState>(initialState)\n\nexport function ThemeProvider({\n  children,\n  defaultTheme = 'dark',\n  storageKey = 'metamorphic-theme',\n  ...props\n}: ThemeProviderProps) {\n  const [theme, setTheme] = useState<Theme>(defaultTheme)\n\n  useEffect(() => {\n    const root = window.document.documentElement\n    root.classList.remove('light', 'dark')\n    root.classList.add(theme)\n  }, [theme])\n\n  useEffect(() => {\n    const stored = localStorage.getItem(storageKey) as Theme\n    if (stored) {\n      setTheme(stored)\n    }\n  }, [storageKey])\n\n  const value = {\n    theme,\n    setTheme: (theme: Theme) => {\n      localStorage.setItem(storageKey, theme)\n      setTheme(theme)\n    },\n    toggleTheme: () => {\n      const newTheme = theme === 'dark' ? 'light' : 'dark'\n      localStorage.setItem(storageKey, newTheme)\n      setTheme(newTheme)\n    },\n  }\n\n  return (\n    <ThemeProviderContext.Provider {...props} value={value}>\n      {children}\n    </ThemeProviderContext.Provider>\n  )\n}\n\nexport const useTheme = () => {\n  const context = useContext(ThemeProviderContext)\n\n  if (context === undefined)\n    throw new Error('useTheme must be used within a ThemeProvider')\n\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAkBA,MAAM,eAAmC;IACvC,OAAO;IACP,UAAU,IAAM;IAChB,aAAa,IAAM;AACrB;AAEA,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAsB;AAExD,SAAS,cAAc,EAC5B,QAAQ,EACR,eAAe,MAAM,EACrB,aAAa,mBAAmB,EAChC,GAAG,OACgB;;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;YAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB;kCAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,SAAS;YACX;QACF;kCAAG;QAAC;KAAW;IAEf,MAAM,QAAQ;QACZ;QACA,UAAU,CAAC;YACT,aAAa,OAAO,CAAC,YAAY;YACjC,SAAS;QACX;QACA,aAAa;YACX,MAAM,WAAW,UAAU,SAAS,UAAU;YAC9C,aAAa,OAAO,CAAC,YAAY;YACjC,SAAS;QACX;IACF;IAEA,qBACE,6LAAC,qBAAqB,QAAQ;QAAE,GAAG,KAAK;QAAE,OAAO;kBAC9C;;;;;;AAGP;GAvCgB;KAAA;AAyCT,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WACd,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;IAPa", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/New%20folder%20%283%29/metamorphic-labs-portfolio/src/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useState } from 'react'\nimport { Moon, Sun, Menu, X } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { useTheme } from '@/components/theme-provider'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Expertise', href: '/expertise' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const { theme, toggleTheme } = useTheme()\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-secondary/20 bg-surface/80 backdrop-blur-md\">\n      <nav className=\"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\" aria-label=\"Global\">\n        {/* Logo */}\n        <div className=\"flex lg:flex-1\">\n          <Link href=\"/\" className=\"-m-1.5 p-1.5\">\n            <span className=\"text-2xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent\">\n              Metamorphic Labs\n            </span>\n          </Link>\n        </div>\n\n        {/* Mobile menu button */}\n        <div className=\"flex lg:hidden\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            className=\"text-text-primary\"\n          >\n            <span className=\"sr-only\">Open main menu</span>\n            {mobileMenuOpen ? (\n              <X className=\"h-6 w-6\" aria-hidden=\"true\" />\n            ) : (\n              <Menu className=\"h-6 w-6\" aria-hidden=\"true\" />\n            )}\n          </Button>\n        </div>\n\n        {/* Desktop navigation */}\n        <div className=\"hidden lg:flex lg:gap-x-12\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'text-sm font-medium transition-colors hover:text-secondary',\n                pathname === item.href\n                  ? 'text-secondary border-b-2 border-secondary'\n                  : 'text-text-primary hover:text-secondary'\n              )}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </div>\n\n        {/* Theme toggle and CTA */}\n        <div className=\"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={toggleTheme}\n            className=\"text-text-primary hover:text-secondary\"\n          >\n            <span className=\"sr-only\">Toggle theme</span>\n            {theme === 'dark' ? (\n              <Sun className=\"h-5 w-5\" />\n            ) : (\n              <Moon className=\"h-5 w-5\" />\n            )}\n          </Button>\n          <Button asChild className=\"bg-accent text-surface hover:bg-accent/90\">\n            <Link href=\"/contact\">Get Started</Link>\n          </Button>\n        </div>\n      </nav>\n\n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <div className=\"lg:hidden\">\n          <div className=\"fixed inset-0 z-50\" />\n          <div className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-surface px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-secondary/20\">\n            <div className=\"flex items-center justify-between\">\n              <Link href=\"/\" className=\"-m-1.5 p-1.5\" onClick={() => setMobileMenuOpen(false)}>\n                <span className=\"text-xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent\">\n                  Metamorphic Labs\n                </span>\n              </Link>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setMobileMenuOpen(false)}\n                className=\"text-text-primary\"\n              >\n                <span className=\"sr-only\">Close menu</span>\n                <X className=\"h-6 w-6\" aria-hidden=\"true\" />\n              </Button>\n            </div>\n            <div className=\"mt-6 flow-root\">\n              <div className=\"-my-6 divide-y divide-secondary/20\">\n                <div className=\"space-y-2 py-6\">\n                  {navigation.map((item) => (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      onClick={() => setMobileMenuOpen(false)}\n                      className={cn(\n                        '-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors',\n                        pathname === item.href\n                          ? 'bg-secondary/10 text-secondary'\n                          : 'text-text-primary hover:bg-secondary/5 hover:text-secondary'\n                      )}\n                    >\n                      {item.name}\n                    </Link>\n                  ))}\n                </div>\n                <div className=\"py-6 space-y-4\">\n                  <Button\n                    variant=\"ghost\"\n                    onClick={toggleTheme}\n                    className=\"w-full justify-start text-text-primary hover:text-secondary\"\n                  >\n                    {theme === 'dark' ? (\n                      <>\n                        <Sun className=\"mr-2 h-5 w-5\" />\n                        Light Mode\n                      </>\n                    ) : (\n                      <>\n                        <Moon className=\"mr-2 h-5 w-5\" />\n                        Dark Mode\n                      </>\n                    )}\n                  </Button>\n                  <Button asChild className=\"w-full bg-accent text-surface hover:bg-accent/90\">\n                    <Link href=\"/contact\" onClick={() => setMobileMenuOpen(false)}>\n                      Get Started\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;gBAAkE,cAAW;;kCAE1F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAK,WAAU;0CAA0G;;;;;;;;;;;;;;;;kCAO9H,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,kBAAkB,CAAC;4BAClC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAU;;;;;;gCACzB,+BACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;oCAAU,eAAY;;;;;yDAEnC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA,aAAa,KAAK,IAAI,GAClB,+CACA;0CAGL,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;kCAepB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAEf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAGpB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;;;;;;;YAM3B,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;wCAAe,SAAS,IAAM,kBAAkB;kDACvE,cAAA,6LAAC;4CAAK,WAAU;sDAAyG;;;;;;;;;;;kDAI3H,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;gDAAU,eAAY;;;;;;;;;;;;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,kBAAkB;oDACjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,aAAa,KAAK,IAAI,GAClB,mCACA;8DAGL,KAAK,IAAI;mDAVL,KAAK,IAAI;;;;;;;;;;sDAcpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;8DAET,UAAU,uBACT;;0EACE,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;qFAIlC;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;8DAKvC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,WAAU;8DACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,SAAS,IAAM,kBAAkB;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnF;GAhJgB;;QACG,qIAAA,CAAA,cAAW;QACG,0IAAA,CAAA,WAAQ;;;KAFzB", "debugId": null}}]}