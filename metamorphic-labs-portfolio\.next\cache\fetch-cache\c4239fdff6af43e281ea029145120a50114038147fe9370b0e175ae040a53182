{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e5420af334c9d-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?order=created_at.desc&select=%2A", "content-profile": "public", "content-range": "0-5/*", "content-type": "application/json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:59:52 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=mZzQbajx7t2_6aYWV3CLl4ZS8NtjjVFIlwikKDvkGi0-1750622392-*******-TdaFVpu5pEYf6KAa__HgGC3gg1rjqikCg84iLub.s04T0HDBc29Nv3mtDHIoYROSRrUHofX10BZPKBMyVwzsNLdU6_g6q5F798MddTJ_xm4; path=/; expires=Sun, 22-Jun-25 20:29:52 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "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", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&order=created_at.desc"}, "revalidate": 31536000, "tags": []}